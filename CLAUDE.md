# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a software license verification and distribution management system built on Cloudflare Workers. It provides license validation services with a two-tier distribution architecture: developers/publishers (admins) and resellers (distributors).

## Architecture

- **Backend**: Cloudflare Workers (TypeScript) - currently basic implementation, designed for Hono framework and D1 integration
- **Frontend**: React 19 + TypeScript with Vite build system
- **Database**: Cloudflare D1 (SQLite) with Prisma ORM
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand for client state
- **Forms**: react-hook-form with Zod validation
- **Deployment**: Wrangler for Cloudflare Workers

## Development Commands

### Backend Commands

Navigate to the `backend/` directory for backend development:

```bash
cd backend
```

- `pnpm run dev` - Start development server with hot reloading
- `pnpm run deploy` - Deploy to Cloudflare Workers
- `pnpm run cf-typegen` - Generate TypeScript types for Cloudflare bindings
- `pnpm run test` - Run tests with Vitest
- `pnpm prisma generate` - Generate Prisma client (outputs to src/generated/prisma)
- `pnpm prisma db push` - Push schema changes to database

### Frontend Commands

Navigate to the `frontend/` directory for frontend development:

```bash
cd frontend
```

- `pnpm run dev` - Start Vite development server with hot reloading
- `pnpm run build` - Build production bundle (TypeScript compilation + Vite build)
- `pnpm run lint` - Run ESLint
- `pnpm run preview` - Preview production build locally

### Database Commands

```bash
# Create D1 database
wrangler d1 create verify-db

# Execute SQL files
wrangler d1 execute verify-db --file=./schema.sql
```

## Configuration

### Wrangler Configuration

The `wrangler.jsonc` file in the backend directory contains:

- D1 database bindings (`DB` bound to "verify" database)
- Assets directory configuration for static files
- Observability enabled for monitoring
- Compatibility flags for fetch behavior

### Frontend Configuration

- **Vite Config**: React plugin with Tailwind CSS via `@tailwindcss/vite`
- **Path Aliases**: `@` mapped to `./src` directory for clean imports
- **Component Libraries**: shadcn/ui with Radix UI primitives
- **Routing**: React Router DOM for client-side navigation

### Environment Variables

Backend environment variables (configured in Wrangler):
- `JWT_SECRET` - JWT signing secret
- `API_VERSION` - API version (v1)
- `ALLOWED_ORIGINS` - CORS allowed origins (configurable per environment)

## Database Schema

### Primary Tables (D1)

- `users` - User accounts (admins and distributors) with role-based permissions
- `products` - Software products (base information)
- `product_versions` - Product versions with pricing, verification templates, cover images, and encryption keys
- `distributor_authorizations` - Distributor product authorization and custom pricing
- `licenses` - License keys with encrypted verification instances

## Frontend Architecture

### Key Components Structure

- **Layout Components**: `components/layout/` - Header, Sidebar, main Layout wrapper
- **UI Components**: `components/ui/` - shadcn/ui components (Button, Card, Form, etc.)
- **Custom Components**: `components/custom/` - Theme provider, color picker, mode toggle
- **Pages**: `pages/` - Dashboard, Login pages with React Router integration
- **Services**: `services/` - API client functions for backend communication
- **Stores**: `stores/` - Zustand stores for auth and UI state management
- **Types**: `types/api.ts` - TypeScript definitions for API requests/responses

### State Management

- **Auth Store**: User authentication state, token management, auto-initialization
- **UI Store**: Theme, sidebar state, and other UI preferences
- **API Client**: Centralized HTTP client with error handling and type safety

## Backend Architecture (Current Implementation)

The backend currently contains a basic Cloudflare Workers setup with:

- **Entry Point**: `src/index.ts` - Basic fetch handler with route switching
- **Database Schema**: Prisma schema with user roles, products, licenses, and distributor authorization
- **Test Configuration**: Vitest with Cloudflare Workers pool for testing

## API Structure (Planned Implementation)

### Client APIs (Public)

- `POST /verify` - License verification endpoint

### Admin APIs (Authenticated)

- Authentication: `POST /admin/login`, `POST /admin/refresh`
- Product management: CRUD operations (admin only)
- License management: Generate, list, revoke, view details
- Distributor management: Create/manage distributors (admin only)
- Statistics: Sales and usage analytics

### Distributor APIs (Authenticated)

- Authentication: `POST /distributor/login`
- Licensed product management: View authorized products
- License generation: Generate licenses for authorized products
- Sales statistics: Personal sales analytics

## Business Logic

### User Roles

- **Admin**: Full system access, product management, distributor creation and authorization
- **Distributor**: Limited to authorized products, license generation, personal statistics

### Verification System

- Template-based verification configuration per product version
- Encrypted verification instances stored with each license
- Real-time verification without caching for immediate consistency
- Support for expiration dates, device limits, feature toggles, etc.

### Distribution Model

1. Admin creates products and versions with verification templates
2. Admin authorizes distributors to sell specific product versions
3. Distributors can set custom pricing for their authorized products
4. Distributors generate licenses for customers
5. End-users verify licenses in real-time

## TypeScript Configuration

The project uses modern TypeScript with:

- ESNext target and modules
- Bundler module resolution
- Strict type checking enabled
- Hono JSX integration
- Cloudflare Workers types via `wrangler types`
- Prisma generated types for database operations

## Key Integration Points

When implementing features:

1. Use Prisma client for all database operations
2. All verification requests query D1 directly for real-time consistency
3. Implement proper role-based authorization checks
4. Use encrypted storage for sensitive verification data
5. Follow the distributor authorization model for access control
