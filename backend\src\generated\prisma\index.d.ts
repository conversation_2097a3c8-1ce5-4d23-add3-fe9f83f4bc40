
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Product
 * 
 */
export type Product = $Result.DefaultSelection<Prisma.$ProductPayload>
/**
 * Model ProductVersion
 * 
 */
export type ProductVersion = $Result.DefaultSelection<Prisma.$ProductVersionPayload>
/**
 * Model Authorization
 * 
 */
export type Authorization = $Result.DefaultSelection<Prisma.$AuthorizationPayload>
/**
 * Model License
 * 
 */
export type License = $Result.DefaultSelection<Prisma.$LicensePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const UserRole: {
  ADMIN: 'ADMIN',
  DISTRIBUTOR: 'DISTRIBUTOR'
};

export type UserRole = (typeof UserRole)[keyof typeof UserRole]


export const UserStatus: {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

export type UserStatus = (typeof UserStatus)[keyof typeof UserStatus]


export const ProductStatus: {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

export type ProductStatus = (typeof ProductStatus)[keyof typeof ProductStatus]


export const AuthorizationStatus: {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

export type AuthorizationStatus = (typeof AuthorizationStatus)[keyof typeof AuthorizationStatus]


export const LicenseStatus: {
  INACTIVE: 'INACTIVE',
  ACTIVE: 'ACTIVE',
  REVOKED: 'REVOKED'
};

export type LicenseStatus = (typeof LicenseStatus)[keyof typeof LicenseStatus]

}

export type UserRole = $Enums.UserRole

export const UserRole: typeof $Enums.UserRole

export type UserStatus = $Enums.UserStatus

export const UserStatus: typeof $Enums.UserStatus

export type ProductStatus = $Enums.ProductStatus

export const ProductStatus: typeof $Enums.ProductStatus

export type AuthorizationStatus = $Enums.AuthorizationStatus

export const AuthorizationStatus: typeof $Enums.AuthorizationStatus

export type LicenseStatus = $Enums.LicenseStatus

export const LicenseStatus: typeof $Enums.LicenseStatus

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  const U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.product`: Exposes CRUD operations for the **Product** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Products
    * const products = await prisma.product.findMany()
    * ```
    */
  get product(): Prisma.ProductDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.productVersion`: Exposes CRUD operations for the **ProductVersion** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ProductVersions
    * const productVersions = await prisma.productVersion.findMany()
    * ```
    */
  get productVersion(): Prisma.ProductVersionDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.authorization`: Exposes CRUD operations for the **Authorization** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Authorizations
    * const authorizations = await prisma.authorization.findMany()
    * ```
    */
  get authorization(): Prisma.AuthorizationDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.license`: Exposes CRUD operations for the **License** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Licenses
    * const licenses = await prisma.license.findMany()
    * ```
    */
  get license(): Prisma.LicenseDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.13.0
   * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Product: 'Product',
    ProductVersion: 'ProductVersion',
    Authorization: 'Authorization',
    License: 'License'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "product" | "productVersion" | "authorization" | "license"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Product: {
        payload: Prisma.$ProductPayload<ExtArgs>
        fields: Prisma.ProductFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ProductFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ProductFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          findFirst: {
            args: Prisma.ProductFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ProductFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          findMany: {
            args: Prisma.ProductFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>[]
          }
          create: {
            args: Prisma.ProductCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          createMany: {
            args: Prisma.ProductCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ProductCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>[]
          }
          delete: {
            args: Prisma.ProductDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          update: {
            args: Prisma.ProductUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          deleteMany: {
            args: Prisma.ProductDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ProductUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ProductUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>[]
          }
          upsert: {
            args: Prisma.ProductUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          aggregate: {
            args: Prisma.ProductAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateProduct>
          }
          groupBy: {
            args: Prisma.ProductGroupByArgs<ExtArgs>
            result: $Utils.Optional<ProductGroupByOutputType>[]
          }
          count: {
            args: Prisma.ProductCountArgs<ExtArgs>
            result: $Utils.Optional<ProductCountAggregateOutputType> | number
          }
        }
      }
      ProductVersion: {
        payload: Prisma.$ProductVersionPayload<ExtArgs>
        fields: Prisma.ProductVersionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ProductVersionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ProductVersionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          findFirst: {
            args: Prisma.ProductVersionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ProductVersionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          findMany: {
            args: Prisma.ProductVersionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>[]
          }
          create: {
            args: Prisma.ProductVersionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          createMany: {
            args: Prisma.ProductVersionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ProductVersionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>[]
          }
          delete: {
            args: Prisma.ProductVersionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          update: {
            args: Prisma.ProductVersionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          deleteMany: {
            args: Prisma.ProductVersionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ProductVersionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ProductVersionUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>[]
          }
          upsert: {
            args: Prisma.ProductVersionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductVersionPayload>
          }
          aggregate: {
            args: Prisma.ProductVersionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateProductVersion>
          }
          groupBy: {
            args: Prisma.ProductVersionGroupByArgs<ExtArgs>
            result: $Utils.Optional<ProductVersionGroupByOutputType>[]
          }
          count: {
            args: Prisma.ProductVersionCountArgs<ExtArgs>
            result: $Utils.Optional<ProductVersionCountAggregateOutputType> | number
          }
        }
      }
      Authorization: {
        payload: Prisma.$AuthorizationPayload<ExtArgs>
        fields: Prisma.AuthorizationFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AuthorizationFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AuthorizationFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          findFirst: {
            args: Prisma.AuthorizationFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AuthorizationFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          findMany: {
            args: Prisma.AuthorizationFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>[]
          }
          create: {
            args: Prisma.AuthorizationCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          createMany: {
            args: Prisma.AuthorizationCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AuthorizationCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>[]
          }
          delete: {
            args: Prisma.AuthorizationDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          update: {
            args: Prisma.AuthorizationUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          deleteMany: {
            args: Prisma.AuthorizationDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AuthorizationUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AuthorizationUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>[]
          }
          upsert: {
            args: Prisma.AuthorizationUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuthorizationPayload>
          }
          aggregate: {
            args: Prisma.AuthorizationAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAuthorization>
          }
          groupBy: {
            args: Prisma.AuthorizationGroupByArgs<ExtArgs>
            result: $Utils.Optional<AuthorizationGroupByOutputType>[]
          }
          count: {
            args: Prisma.AuthorizationCountArgs<ExtArgs>
            result: $Utils.Optional<AuthorizationCountAggregateOutputType> | number
          }
        }
      }
      License: {
        payload: Prisma.$LicensePayload<ExtArgs>
        fields: Prisma.LicenseFieldRefs
        operations: {
          findUnique: {
            args: Prisma.LicenseFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.LicenseFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          findFirst: {
            args: Prisma.LicenseFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.LicenseFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          findMany: {
            args: Prisma.LicenseFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>[]
          }
          create: {
            args: Prisma.LicenseCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          createMany: {
            args: Prisma.LicenseCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.LicenseCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>[]
          }
          delete: {
            args: Prisma.LicenseDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          update: {
            args: Prisma.LicenseUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          deleteMany: {
            args: Prisma.LicenseDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.LicenseUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.LicenseUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>[]
          }
          upsert: {
            args: Prisma.LicenseUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$LicensePayload>
          }
          aggregate: {
            args: Prisma.LicenseAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateLicense>
          }
          groupBy: {
            args: Prisma.LicenseGroupByArgs<ExtArgs>
            result: $Utils.Optional<LicenseGroupByOutputType>[]
          }
          count: {
            args: Prisma.LicenseCountArgs<ExtArgs>
            result: $Utils.Optional<LicenseCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Shorthand for `emit: 'stdout'`
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events only
     * log: [
     *   { emit: 'event', level: 'query' },
     *   { emit: 'event', level: 'info' },
     *   { emit: 'event', level: 'warn' }
     *   { emit: 'event', level: 'error' }
     * ]
     * 
     * / Emit as events and log to stdout
     * og: [
     *  { emit: 'stdout', level: 'query' },
     *  { emit: 'stdout', level: 'info' },
     *  { emit: 'stdout', level: 'warn' }
     *  { emit: 'stdout', level: 'error' }
     * 
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Instance of a Driver Adapter, e.g., like one provided by `@prisma/adapter-planetscale`
     */
    adapter?: runtime.SqlDriverAdapterFactory | null
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    product?: ProductOmit
    productVersion?: ProductVersionOmit
    authorization?: AuthorizationOmit
    license?: LicenseOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

  export type GetLogType<T> = CheckIsLogLevel<
    T extends LogDefinition ? T['level'] : T
  >;

  export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
    ? GetLogType<T[number]>
    : never;

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    licenses: number
    authorizations: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    licenses?: boolean | UserCountOutputTypeCountLicensesArgs
    authorizations?: boolean | UserCountOutputTypeCountAuthorizationsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountLicensesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: LicenseWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountAuthorizationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AuthorizationWhereInput
  }


  /**
   * Count Type ProductCountOutputType
   */

  export type ProductCountOutputType = {
    versions: number
  }

  export type ProductCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    versions?: boolean | ProductCountOutputTypeCountVersionsArgs
  }

  // Custom InputTypes
  /**
   * ProductCountOutputType without action
   */
  export type ProductCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductCountOutputType
     */
    select?: ProductCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ProductCountOutputType without action
   */
  export type ProductCountOutputTypeCountVersionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductVersionWhereInput
  }


  /**
   * Count Type ProductVersionCountOutputType
   */

  export type ProductVersionCountOutputType = {
    licenses: number
    authorizations: number
  }

  export type ProductVersionCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    licenses?: boolean | ProductVersionCountOutputTypeCountLicensesArgs
    authorizations?: boolean | ProductVersionCountOutputTypeCountAuthorizationsArgs
  }

  // Custom InputTypes
  /**
   * ProductVersionCountOutputType without action
   */
  export type ProductVersionCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersionCountOutputType
     */
    select?: ProductVersionCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ProductVersionCountOutputType without action
   */
  export type ProductVersionCountOutputTypeCountLicensesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: LicenseWhereInput
  }

  /**
   * ProductVersionCountOutputType without action
   */
  export type ProductVersionCountOutputTypeCountAuthorizationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AuthorizationWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    id: number | null
  }

  export type UserSumAggregateOutputType = {
    id: number | null
  }

  export type UserMinAggregateOutputType = {
    id: number | null
    username: string | null
    passwordHash: string | null
    role: $Enums.UserRole | null
    status: $Enums.UserStatus | null
    nickName: string | null
    wechat: string | null
    avatar: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: number | null
    username: string | null
    passwordHash: string | null
    role: $Enums.UserRole | null
    status: $Enums.UserStatus | null
    nickName: string | null
    wechat: string | null
    avatar: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    username: number
    passwordHash: number
    role: number
    status: number
    nickName: number
    wechat: number
    avatar: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    id?: true
  }

  export type UserSumAggregateInputType = {
    id?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    username?: true
    passwordHash?: true
    role?: true
    status?: true
    nickName?: true
    wechat?: true
    avatar?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    username?: true
    passwordHash?: true
    role?: true
    status?: true
    nickName?: true
    wechat?: true
    avatar?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    username?: true
    passwordHash?: true
    role?: true
    status?: true
    nickName?: true
    wechat?: true
    avatar?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: number
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status: $Enums.UserStatus
    nickName: string | null
    wechat: string | null
    avatar: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    passwordHash?: boolean
    role?: boolean
    status?: boolean
    nickName?: boolean
    wechat?: boolean
    avatar?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    licenses?: boolean | User$licensesArgs<ExtArgs>
    authorizations?: boolean | User$authorizationsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    passwordHash?: boolean
    role?: boolean
    status?: boolean
    nickName?: boolean
    wechat?: boolean
    avatar?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    passwordHash?: boolean
    role?: boolean
    status?: boolean
    nickName?: boolean
    wechat?: boolean
    avatar?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    username?: boolean
    passwordHash?: boolean
    role?: boolean
    status?: boolean
    nickName?: boolean
    wechat?: boolean
    avatar?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "username" | "passwordHash" | "role" | "status" | "nickName" | "wechat" | "avatar" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    licenses?: boolean | User$licensesArgs<ExtArgs>
    authorizations?: boolean | User$authorizationsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      licenses: Prisma.$LicensePayload<ExtArgs>[]
      authorizations: Prisma.$AuthorizationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      username: string
      passwordHash: string
      role: $Enums.UserRole
      status: $Enums.UserStatus
      nickName: string | null
      wechat: string | null
      avatar: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    licenses<T extends User$licensesArgs<ExtArgs> = {}>(args?: Subset<T, User$licensesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    authorizations<T extends User$authorizationsArgs<ExtArgs> = {}>(args?: Subset<T, User$authorizationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'Int'>
    readonly username: FieldRef<"User", 'String'>
    readonly passwordHash: FieldRef<"User", 'String'>
    readonly role: FieldRef<"User", 'UserRole'>
    readonly status: FieldRef<"User", 'UserStatus'>
    readonly nickName: FieldRef<"User", 'String'>
    readonly wechat: FieldRef<"User", 'String'>
    readonly avatar: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.licenses
   */
  export type User$licensesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    where?: LicenseWhereInput
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    cursor?: LicenseWhereUniqueInput
    take?: number
    skip?: number
    distinct?: LicenseScalarFieldEnum | LicenseScalarFieldEnum[]
  }

  /**
   * User.authorizations
   */
  export type User$authorizationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    where?: AuthorizationWhereInput
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    cursor?: AuthorizationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AuthorizationScalarFieldEnum | AuthorizationScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Product
   */

  export type AggregateProduct = {
    _count: ProductCountAggregateOutputType | null
    _avg: ProductAvgAggregateOutputType | null
    _sum: ProductSumAggregateOutputType | null
    _min: ProductMinAggregateOutputType | null
    _max: ProductMaxAggregateOutputType | null
  }

  export type ProductAvgAggregateOutputType = {
    id: number | null
  }

  export type ProductSumAggregateOutputType = {
    id: number | null
  }

  export type ProductMinAggregateOutputType = {
    id: number | null
    name: string | null
    description: string | null
    category: string | null
    status: $Enums.ProductStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductMaxAggregateOutputType = {
    id: number | null
    name: string | null
    description: string | null
    category: string | null
    status: $Enums.ProductStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductCountAggregateOutputType = {
    id: number
    name: number
    description: number
    category: number
    status: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ProductAvgAggregateInputType = {
    id?: true
  }

  export type ProductSumAggregateInputType = {
    id?: true
  }

  export type ProductMinAggregateInputType = {
    id?: true
    name?: true
    description?: true
    category?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductMaxAggregateInputType = {
    id?: true
    name?: true
    description?: true
    category?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductCountAggregateInputType = {
    id?: true
    name?: true
    description?: true
    category?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ProductAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Product to aggregate.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Products
    **/
    _count?: true | ProductCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ProductAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ProductSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ProductMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ProductMaxAggregateInputType
  }

  export type GetProductAggregateType<T extends ProductAggregateArgs> = {
        [P in keyof T & keyof AggregateProduct]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateProduct[P]>
      : GetScalarType<T[P], AggregateProduct[P]>
  }




  export type ProductGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductWhereInput
    orderBy?: ProductOrderByWithAggregationInput | ProductOrderByWithAggregationInput[]
    by: ProductScalarFieldEnum[] | ProductScalarFieldEnum
    having?: ProductScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ProductCountAggregateInputType | true
    _avg?: ProductAvgAggregateInputType
    _sum?: ProductSumAggregateInputType
    _min?: ProductMinAggregateInputType
    _max?: ProductMaxAggregateInputType
  }

  export type ProductGroupByOutputType = {
    id: number
    name: string
    description: string | null
    category: string | null
    status: $Enums.ProductStatus
    createdAt: Date
    updatedAt: Date
    _count: ProductCountAggregateOutputType | null
    _avg: ProductAvgAggregateOutputType | null
    _sum: ProductSumAggregateOutputType | null
    _min: ProductMinAggregateOutputType | null
    _max: ProductMaxAggregateOutputType | null
  }

  type GetProductGroupByPayload<T extends ProductGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ProductGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ProductGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ProductGroupByOutputType[P]>
            : GetScalarType<T[P], ProductGroupByOutputType[P]>
        }
      >
    >


  export type ProductSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    category?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    versions?: boolean | Product$versionsArgs<ExtArgs>
    _count?: boolean | ProductCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["product"]>

  export type ProductSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    category?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["product"]>

  export type ProductSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    category?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["product"]>

  export type ProductSelectScalar = {
    id?: boolean
    name?: boolean
    description?: boolean
    category?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ProductOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "description" | "category" | "status" | "createdAt" | "updatedAt", ExtArgs["result"]["product"]>
  export type ProductInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    versions?: boolean | Product$versionsArgs<ExtArgs>
    _count?: boolean | ProductCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ProductIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type ProductIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $ProductPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Product"
    objects: {
      versions: Prisma.$ProductVersionPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      name: string
      description: string | null
      category: string | null
      status: $Enums.ProductStatus
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["product"]>
    composites: {}
  }

  type ProductGetPayload<S extends boolean | null | undefined | ProductDefaultArgs> = $Result.GetResult<Prisma.$ProductPayload, S>

  type ProductCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ProductFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ProductCountAggregateInputType | true
    }

  export interface ProductDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Product'], meta: { name: 'Product' } }
    /**
     * Find zero or one Product that matches the filter.
     * @param {ProductFindUniqueArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ProductFindUniqueArgs>(args: SelectSubset<T, ProductFindUniqueArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Product that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ProductFindUniqueOrThrowArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ProductFindUniqueOrThrowArgs>(args: SelectSubset<T, ProductFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Product that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindFirstArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ProductFindFirstArgs>(args?: SelectSubset<T, ProductFindFirstArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Product that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindFirstOrThrowArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ProductFindFirstOrThrowArgs>(args?: SelectSubset<T, ProductFindFirstOrThrowArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Products that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Products
     * const products = await prisma.product.findMany()
     * 
     * // Get first 10 Products
     * const products = await prisma.product.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const productWithIdOnly = await prisma.product.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ProductFindManyArgs>(args?: SelectSubset<T, ProductFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Product.
     * @param {ProductCreateArgs} args - Arguments to create a Product.
     * @example
     * // Create one Product
     * const Product = await prisma.product.create({
     *   data: {
     *     // ... data to create a Product
     *   }
     * })
     * 
     */
    create<T extends ProductCreateArgs>(args: SelectSubset<T, ProductCreateArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Products.
     * @param {ProductCreateManyArgs} args - Arguments to create many Products.
     * @example
     * // Create many Products
     * const product = await prisma.product.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ProductCreateManyArgs>(args?: SelectSubset<T, ProductCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Products and returns the data saved in the database.
     * @param {ProductCreateManyAndReturnArgs} args - Arguments to create many Products.
     * @example
     * // Create many Products
     * const product = await prisma.product.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Products and only return the `id`
     * const productWithIdOnly = await prisma.product.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ProductCreateManyAndReturnArgs>(args?: SelectSubset<T, ProductCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Product.
     * @param {ProductDeleteArgs} args - Arguments to delete one Product.
     * @example
     * // Delete one Product
     * const Product = await prisma.product.delete({
     *   where: {
     *     // ... filter to delete one Product
     *   }
     * })
     * 
     */
    delete<T extends ProductDeleteArgs>(args: SelectSubset<T, ProductDeleteArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Product.
     * @param {ProductUpdateArgs} args - Arguments to update one Product.
     * @example
     * // Update one Product
     * const product = await prisma.product.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ProductUpdateArgs>(args: SelectSubset<T, ProductUpdateArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Products.
     * @param {ProductDeleteManyArgs} args - Arguments to filter Products to delete.
     * @example
     * // Delete a few Products
     * const { count } = await prisma.product.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ProductDeleteManyArgs>(args?: SelectSubset<T, ProductDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Products.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Products
     * const product = await prisma.product.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ProductUpdateManyArgs>(args: SelectSubset<T, ProductUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Products and returns the data updated in the database.
     * @param {ProductUpdateManyAndReturnArgs} args - Arguments to update many Products.
     * @example
     * // Update many Products
     * const product = await prisma.product.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Products and only return the `id`
     * const productWithIdOnly = await prisma.product.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ProductUpdateManyAndReturnArgs>(args: SelectSubset<T, ProductUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Product.
     * @param {ProductUpsertArgs} args - Arguments to update or create a Product.
     * @example
     * // Update or create a Product
     * const product = await prisma.product.upsert({
     *   create: {
     *     // ... data to create a Product
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Product we want to update
     *   }
     * })
     */
    upsert<T extends ProductUpsertArgs>(args: SelectSubset<T, ProductUpsertArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Products.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductCountArgs} args - Arguments to filter Products to count.
     * @example
     * // Count the number of Products
     * const count = await prisma.product.count({
     *   where: {
     *     // ... the filter for the Products we want to count
     *   }
     * })
    **/
    count<T extends ProductCountArgs>(
      args?: Subset<T, ProductCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ProductCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Product.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ProductAggregateArgs>(args: Subset<T, ProductAggregateArgs>): Prisma.PrismaPromise<GetProductAggregateType<T>>

    /**
     * Group by Product.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ProductGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ProductGroupByArgs['orderBy'] }
        : { orderBy?: ProductGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ProductGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProductGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Product model
   */
  readonly fields: ProductFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Product.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ProductClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    versions<T extends Product$versionsArgs<ExtArgs> = {}>(args?: Subset<T, Product$versionsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Product model
   */
  interface ProductFieldRefs {
    readonly id: FieldRef<"Product", 'Int'>
    readonly name: FieldRef<"Product", 'String'>
    readonly description: FieldRef<"Product", 'String'>
    readonly category: FieldRef<"Product", 'String'>
    readonly status: FieldRef<"Product", 'ProductStatus'>
    readonly createdAt: FieldRef<"Product", 'DateTime'>
    readonly updatedAt: FieldRef<"Product", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Product findUnique
   */
  export type ProductFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product findUniqueOrThrow
   */
  export type ProductFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product findFirst
   */
  export type ProductFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Products.
     */
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product findFirstOrThrow
   */
  export type ProductFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Products.
     */
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product findMany
   */
  export type ProductFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Products to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product create
   */
  export type ProductCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The data needed to create a Product.
     */
    data: XOR<ProductCreateInput, ProductUncheckedCreateInput>
  }

  /**
   * Product createMany
   */
  export type ProductCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Products.
     */
    data: ProductCreateManyInput | ProductCreateManyInput[]
  }

  /**
   * Product createManyAndReturn
   */
  export type ProductCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * The data used to create many Products.
     */
    data: ProductCreateManyInput | ProductCreateManyInput[]
  }

  /**
   * Product update
   */
  export type ProductUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The data needed to update a Product.
     */
    data: XOR<ProductUpdateInput, ProductUncheckedUpdateInput>
    /**
     * Choose, which Product to update.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product updateMany
   */
  export type ProductUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Products.
     */
    data: XOR<ProductUpdateManyMutationInput, ProductUncheckedUpdateManyInput>
    /**
     * Filter which Products to update
     */
    where?: ProductWhereInput
    /**
     * Limit how many Products to update.
     */
    limit?: number
  }

  /**
   * Product updateManyAndReturn
   */
  export type ProductUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * The data used to update Products.
     */
    data: XOR<ProductUpdateManyMutationInput, ProductUncheckedUpdateManyInput>
    /**
     * Filter which Products to update
     */
    where?: ProductWhereInput
    /**
     * Limit how many Products to update.
     */
    limit?: number
  }

  /**
   * Product upsert
   */
  export type ProductUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The filter to search for the Product to update in case it exists.
     */
    where: ProductWhereUniqueInput
    /**
     * In case the Product found by the `where` argument doesn't exist, create a new Product with this data.
     */
    create: XOR<ProductCreateInput, ProductUncheckedCreateInput>
    /**
     * In case the Product was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ProductUpdateInput, ProductUncheckedUpdateInput>
  }

  /**
   * Product delete
   */
  export type ProductDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter which Product to delete.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product deleteMany
   */
  export type ProductDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Products to delete
     */
    where?: ProductWhereInput
    /**
     * Limit how many Products to delete.
     */
    limit?: number
  }

  /**
   * Product.versions
   */
  export type Product$versionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    where?: ProductVersionWhereInput
    orderBy?: ProductVersionOrderByWithRelationInput | ProductVersionOrderByWithRelationInput[]
    cursor?: ProductVersionWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ProductVersionScalarFieldEnum | ProductVersionScalarFieldEnum[]
  }

  /**
   * Product without action
   */
  export type ProductDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Product
     */
    omit?: ProductOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
  }


  /**
   * Model ProductVersion
   */

  export type AggregateProductVersion = {
    _count: ProductVersionCountAggregateOutputType | null
    _avg: ProductVersionAvgAggregateOutputType | null
    _sum: ProductVersionSumAggregateOutputType | null
    _min: ProductVersionMinAggregateOutputType | null
    _max: ProductVersionMaxAggregateOutputType | null
  }

  export type ProductVersionAvgAggregateOutputType = {
    id: number | null
    productId: number | null
    defaultPrice: number | null
  }

  export type ProductVersionSumAggregateOutputType = {
    id: number | null
    productId: number | null
    defaultPrice: number | null
  }

  export type ProductVersionMinAggregateOutputType = {
    id: number | null
    productId: number | null
    version: string | null
    versionName: string | null
    description: string | null
    verifyTemplate: string | null
    encryptionKey: string | null
    defaultPrice: number | null
    downloadLink: string | null
    coverUrl: string | null
    changelog: string | null
    status: $Enums.ProductStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductVersionMaxAggregateOutputType = {
    id: number | null
    productId: number | null
    version: string | null
    versionName: string | null
    description: string | null
    verifyTemplate: string | null
    encryptionKey: string | null
    defaultPrice: number | null
    downloadLink: string | null
    coverUrl: string | null
    changelog: string | null
    status: $Enums.ProductStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductVersionCountAggregateOutputType = {
    id: number
    productId: number
    version: number
    versionName: number
    description: number
    verifyTemplate: number
    encryptionKey: number
    defaultPrice: number
    downloadLink: number
    coverUrl: number
    changelog: number
    status: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ProductVersionAvgAggregateInputType = {
    id?: true
    productId?: true
    defaultPrice?: true
  }

  export type ProductVersionSumAggregateInputType = {
    id?: true
    productId?: true
    defaultPrice?: true
  }

  export type ProductVersionMinAggregateInputType = {
    id?: true
    productId?: true
    version?: true
    versionName?: true
    description?: true
    verifyTemplate?: true
    encryptionKey?: true
    defaultPrice?: true
    downloadLink?: true
    coverUrl?: true
    changelog?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductVersionMaxAggregateInputType = {
    id?: true
    productId?: true
    version?: true
    versionName?: true
    description?: true
    verifyTemplate?: true
    encryptionKey?: true
    defaultPrice?: true
    downloadLink?: true
    coverUrl?: true
    changelog?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductVersionCountAggregateInputType = {
    id?: true
    productId?: true
    version?: true
    versionName?: true
    description?: true
    verifyTemplate?: true
    encryptionKey?: true
    defaultPrice?: true
    downloadLink?: true
    coverUrl?: true
    changelog?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ProductVersionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ProductVersion to aggregate.
     */
    where?: ProductVersionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductVersions to fetch.
     */
    orderBy?: ProductVersionOrderByWithRelationInput | ProductVersionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ProductVersionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductVersions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductVersions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ProductVersions
    **/
    _count?: true | ProductVersionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ProductVersionAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ProductVersionSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ProductVersionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ProductVersionMaxAggregateInputType
  }

  export type GetProductVersionAggregateType<T extends ProductVersionAggregateArgs> = {
        [P in keyof T & keyof AggregateProductVersion]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateProductVersion[P]>
      : GetScalarType<T[P], AggregateProductVersion[P]>
  }




  export type ProductVersionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductVersionWhereInput
    orderBy?: ProductVersionOrderByWithAggregationInput | ProductVersionOrderByWithAggregationInput[]
    by: ProductVersionScalarFieldEnum[] | ProductVersionScalarFieldEnum
    having?: ProductVersionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ProductVersionCountAggregateInputType | true
    _avg?: ProductVersionAvgAggregateInputType
    _sum?: ProductVersionSumAggregateInputType
    _min?: ProductVersionMinAggregateInputType
    _max?: ProductVersionMaxAggregateInputType
  }

  export type ProductVersionGroupByOutputType = {
    id: number
    productId: number
    version: string
    versionName: string | null
    description: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink: string | null
    coverUrl: string | null
    changelog: string | null
    status: $Enums.ProductStatus
    createdAt: Date
    updatedAt: Date
    _count: ProductVersionCountAggregateOutputType | null
    _avg: ProductVersionAvgAggregateOutputType | null
    _sum: ProductVersionSumAggregateOutputType | null
    _min: ProductVersionMinAggregateOutputType | null
    _max: ProductVersionMaxAggregateOutputType | null
  }

  type GetProductVersionGroupByPayload<T extends ProductVersionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ProductVersionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ProductVersionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ProductVersionGroupByOutputType[P]>
            : GetScalarType<T[P], ProductVersionGroupByOutputType[P]>
        }
      >
    >


  export type ProductVersionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    productId?: boolean
    version?: boolean
    versionName?: boolean
    description?: boolean
    verifyTemplate?: boolean
    encryptionKey?: boolean
    defaultPrice?: boolean
    downloadLink?: boolean
    coverUrl?: boolean
    changelog?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    product?: boolean | ProductDefaultArgs<ExtArgs>
    licenses?: boolean | ProductVersion$licensesArgs<ExtArgs>
    authorizations?: boolean | ProductVersion$authorizationsArgs<ExtArgs>
    _count?: boolean | ProductVersionCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productVersion"]>

  export type ProductVersionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    productId?: boolean
    version?: boolean
    versionName?: boolean
    description?: boolean
    verifyTemplate?: boolean
    encryptionKey?: boolean
    defaultPrice?: boolean
    downloadLink?: boolean
    coverUrl?: boolean
    changelog?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    product?: boolean | ProductDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productVersion"]>

  export type ProductVersionSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    productId?: boolean
    version?: boolean
    versionName?: boolean
    description?: boolean
    verifyTemplate?: boolean
    encryptionKey?: boolean
    defaultPrice?: boolean
    downloadLink?: boolean
    coverUrl?: boolean
    changelog?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    product?: boolean | ProductDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productVersion"]>

  export type ProductVersionSelectScalar = {
    id?: boolean
    productId?: boolean
    version?: boolean
    versionName?: boolean
    description?: boolean
    verifyTemplate?: boolean
    encryptionKey?: boolean
    defaultPrice?: boolean
    downloadLink?: boolean
    coverUrl?: boolean
    changelog?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ProductVersionOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "productId" | "version" | "versionName" | "description" | "verifyTemplate" | "encryptionKey" | "defaultPrice" | "downloadLink" | "coverUrl" | "changelog" | "status" | "createdAt" | "updatedAt", ExtArgs["result"]["productVersion"]>
  export type ProductVersionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    product?: boolean | ProductDefaultArgs<ExtArgs>
    licenses?: boolean | ProductVersion$licensesArgs<ExtArgs>
    authorizations?: boolean | ProductVersion$authorizationsArgs<ExtArgs>
    _count?: boolean | ProductVersionCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ProductVersionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    product?: boolean | ProductDefaultArgs<ExtArgs>
  }
  export type ProductVersionIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    product?: boolean | ProductDefaultArgs<ExtArgs>
  }

  export type $ProductVersionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ProductVersion"
    objects: {
      product: Prisma.$ProductPayload<ExtArgs>
      licenses: Prisma.$LicensePayload<ExtArgs>[]
      authorizations: Prisma.$AuthorizationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      productId: number
      version: string
      versionName: string | null
      description: string | null
      verifyTemplate: string
      encryptionKey: string
      defaultPrice: number
      downloadLink: string | null
      coverUrl: string | null
      changelog: string | null
      status: $Enums.ProductStatus
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["productVersion"]>
    composites: {}
  }

  type ProductVersionGetPayload<S extends boolean | null | undefined | ProductVersionDefaultArgs> = $Result.GetResult<Prisma.$ProductVersionPayload, S>

  type ProductVersionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ProductVersionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ProductVersionCountAggregateInputType | true
    }

  export interface ProductVersionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ProductVersion'], meta: { name: 'ProductVersion' } }
    /**
     * Find zero or one ProductVersion that matches the filter.
     * @param {ProductVersionFindUniqueArgs} args - Arguments to find a ProductVersion
     * @example
     * // Get one ProductVersion
     * const productVersion = await prisma.productVersion.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ProductVersionFindUniqueArgs>(args: SelectSubset<T, ProductVersionFindUniqueArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ProductVersion that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ProductVersionFindUniqueOrThrowArgs} args - Arguments to find a ProductVersion
     * @example
     * // Get one ProductVersion
     * const productVersion = await prisma.productVersion.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ProductVersionFindUniqueOrThrowArgs>(args: SelectSubset<T, ProductVersionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ProductVersion that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionFindFirstArgs} args - Arguments to find a ProductVersion
     * @example
     * // Get one ProductVersion
     * const productVersion = await prisma.productVersion.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ProductVersionFindFirstArgs>(args?: SelectSubset<T, ProductVersionFindFirstArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ProductVersion that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionFindFirstOrThrowArgs} args - Arguments to find a ProductVersion
     * @example
     * // Get one ProductVersion
     * const productVersion = await prisma.productVersion.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ProductVersionFindFirstOrThrowArgs>(args?: SelectSubset<T, ProductVersionFindFirstOrThrowArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ProductVersions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ProductVersions
     * const productVersions = await prisma.productVersion.findMany()
     * 
     * // Get first 10 ProductVersions
     * const productVersions = await prisma.productVersion.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const productVersionWithIdOnly = await prisma.productVersion.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ProductVersionFindManyArgs>(args?: SelectSubset<T, ProductVersionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ProductVersion.
     * @param {ProductVersionCreateArgs} args - Arguments to create a ProductVersion.
     * @example
     * // Create one ProductVersion
     * const ProductVersion = await prisma.productVersion.create({
     *   data: {
     *     // ... data to create a ProductVersion
     *   }
     * })
     * 
     */
    create<T extends ProductVersionCreateArgs>(args: SelectSubset<T, ProductVersionCreateArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ProductVersions.
     * @param {ProductVersionCreateManyArgs} args - Arguments to create many ProductVersions.
     * @example
     * // Create many ProductVersions
     * const productVersion = await prisma.productVersion.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ProductVersionCreateManyArgs>(args?: SelectSubset<T, ProductVersionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ProductVersions and returns the data saved in the database.
     * @param {ProductVersionCreateManyAndReturnArgs} args - Arguments to create many ProductVersions.
     * @example
     * // Create many ProductVersions
     * const productVersion = await prisma.productVersion.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ProductVersions and only return the `id`
     * const productVersionWithIdOnly = await prisma.productVersion.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ProductVersionCreateManyAndReturnArgs>(args?: SelectSubset<T, ProductVersionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ProductVersion.
     * @param {ProductVersionDeleteArgs} args - Arguments to delete one ProductVersion.
     * @example
     * // Delete one ProductVersion
     * const ProductVersion = await prisma.productVersion.delete({
     *   where: {
     *     // ... filter to delete one ProductVersion
     *   }
     * })
     * 
     */
    delete<T extends ProductVersionDeleteArgs>(args: SelectSubset<T, ProductVersionDeleteArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ProductVersion.
     * @param {ProductVersionUpdateArgs} args - Arguments to update one ProductVersion.
     * @example
     * // Update one ProductVersion
     * const productVersion = await prisma.productVersion.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ProductVersionUpdateArgs>(args: SelectSubset<T, ProductVersionUpdateArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ProductVersions.
     * @param {ProductVersionDeleteManyArgs} args - Arguments to filter ProductVersions to delete.
     * @example
     * // Delete a few ProductVersions
     * const { count } = await prisma.productVersion.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ProductVersionDeleteManyArgs>(args?: SelectSubset<T, ProductVersionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ProductVersions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ProductVersions
     * const productVersion = await prisma.productVersion.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ProductVersionUpdateManyArgs>(args: SelectSubset<T, ProductVersionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ProductVersions and returns the data updated in the database.
     * @param {ProductVersionUpdateManyAndReturnArgs} args - Arguments to update many ProductVersions.
     * @example
     * // Update many ProductVersions
     * const productVersion = await prisma.productVersion.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ProductVersions and only return the `id`
     * const productVersionWithIdOnly = await prisma.productVersion.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ProductVersionUpdateManyAndReturnArgs>(args: SelectSubset<T, ProductVersionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ProductVersion.
     * @param {ProductVersionUpsertArgs} args - Arguments to update or create a ProductVersion.
     * @example
     * // Update or create a ProductVersion
     * const productVersion = await prisma.productVersion.upsert({
     *   create: {
     *     // ... data to create a ProductVersion
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ProductVersion we want to update
     *   }
     * })
     */
    upsert<T extends ProductVersionUpsertArgs>(args: SelectSubset<T, ProductVersionUpsertArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ProductVersions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionCountArgs} args - Arguments to filter ProductVersions to count.
     * @example
     * // Count the number of ProductVersions
     * const count = await prisma.productVersion.count({
     *   where: {
     *     // ... the filter for the ProductVersions we want to count
     *   }
     * })
    **/
    count<T extends ProductVersionCountArgs>(
      args?: Subset<T, ProductVersionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ProductVersionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ProductVersion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ProductVersionAggregateArgs>(args: Subset<T, ProductVersionAggregateArgs>): Prisma.PrismaPromise<GetProductVersionAggregateType<T>>

    /**
     * Group by ProductVersion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductVersionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ProductVersionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ProductVersionGroupByArgs['orderBy'] }
        : { orderBy?: ProductVersionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ProductVersionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProductVersionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ProductVersion model
   */
  readonly fields: ProductVersionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ProductVersion.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ProductVersionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    product<T extends ProductDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ProductDefaultArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    licenses<T extends ProductVersion$licensesArgs<ExtArgs> = {}>(args?: Subset<T, ProductVersion$licensesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    authorizations<T extends ProductVersion$authorizationsArgs<ExtArgs> = {}>(args?: Subset<T, ProductVersion$authorizationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ProductVersion model
   */
  interface ProductVersionFieldRefs {
    readonly id: FieldRef<"ProductVersion", 'Int'>
    readonly productId: FieldRef<"ProductVersion", 'Int'>
    readonly version: FieldRef<"ProductVersion", 'String'>
    readonly versionName: FieldRef<"ProductVersion", 'String'>
    readonly description: FieldRef<"ProductVersion", 'String'>
    readonly verifyTemplate: FieldRef<"ProductVersion", 'String'>
    readonly encryptionKey: FieldRef<"ProductVersion", 'String'>
    readonly defaultPrice: FieldRef<"ProductVersion", 'Float'>
    readonly downloadLink: FieldRef<"ProductVersion", 'String'>
    readonly coverUrl: FieldRef<"ProductVersion", 'String'>
    readonly changelog: FieldRef<"ProductVersion", 'String'>
    readonly status: FieldRef<"ProductVersion", 'ProductStatus'>
    readonly createdAt: FieldRef<"ProductVersion", 'DateTime'>
    readonly updatedAt: FieldRef<"ProductVersion", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ProductVersion findUnique
   */
  export type ProductVersionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter, which ProductVersion to fetch.
     */
    where: ProductVersionWhereUniqueInput
  }

  /**
   * ProductVersion findUniqueOrThrow
   */
  export type ProductVersionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter, which ProductVersion to fetch.
     */
    where: ProductVersionWhereUniqueInput
  }

  /**
   * ProductVersion findFirst
   */
  export type ProductVersionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter, which ProductVersion to fetch.
     */
    where?: ProductVersionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductVersions to fetch.
     */
    orderBy?: ProductVersionOrderByWithRelationInput | ProductVersionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ProductVersions.
     */
    cursor?: ProductVersionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductVersions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductVersions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ProductVersions.
     */
    distinct?: ProductVersionScalarFieldEnum | ProductVersionScalarFieldEnum[]
  }

  /**
   * ProductVersion findFirstOrThrow
   */
  export type ProductVersionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter, which ProductVersion to fetch.
     */
    where?: ProductVersionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductVersions to fetch.
     */
    orderBy?: ProductVersionOrderByWithRelationInput | ProductVersionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ProductVersions.
     */
    cursor?: ProductVersionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductVersions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductVersions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ProductVersions.
     */
    distinct?: ProductVersionScalarFieldEnum | ProductVersionScalarFieldEnum[]
  }

  /**
   * ProductVersion findMany
   */
  export type ProductVersionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter, which ProductVersions to fetch.
     */
    where?: ProductVersionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductVersions to fetch.
     */
    orderBy?: ProductVersionOrderByWithRelationInput | ProductVersionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ProductVersions.
     */
    cursor?: ProductVersionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductVersions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductVersions.
     */
    skip?: number
    distinct?: ProductVersionScalarFieldEnum | ProductVersionScalarFieldEnum[]
  }

  /**
   * ProductVersion create
   */
  export type ProductVersionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * The data needed to create a ProductVersion.
     */
    data: XOR<ProductVersionCreateInput, ProductVersionUncheckedCreateInput>
  }

  /**
   * ProductVersion createMany
   */
  export type ProductVersionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ProductVersions.
     */
    data: ProductVersionCreateManyInput | ProductVersionCreateManyInput[]
  }

  /**
   * ProductVersion createManyAndReturn
   */
  export type ProductVersionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * The data used to create many ProductVersions.
     */
    data: ProductVersionCreateManyInput | ProductVersionCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ProductVersion update
   */
  export type ProductVersionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * The data needed to update a ProductVersion.
     */
    data: XOR<ProductVersionUpdateInput, ProductVersionUncheckedUpdateInput>
    /**
     * Choose, which ProductVersion to update.
     */
    where: ProductVersionWhereUniqueInput
  }

  /**
   * ProductVersion updateMany
   */
  export type ProductVersionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ProductVersions.
     */
    data: XOR<ProductVersionUpdateManyMutationInput, ProductVersionUncheckedUpdateManyInput>
    /**
     * Filter which ProductVersions to update
     */
    where?: ProductVersionWhereInput
    /**
     * Limit how many ProductVersions to update.
     */
    limit?: number
  }

  /**
   * ProductVersion updateManyAndReturn
   */
  export type ProductVersionUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * The data used to update ProductVersions.
     */
    data: XOR<ProductVersionUpdateManyMutationInput, ProductVersionUncheckedUpdateManyInput>
    /**
     * Filter which ProductVersions to update
     */
    where?: ProductVersionWhereInput
    /**
     * Limit how many ProductVersions to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * ProductVersion upsert
   */
  export type ProductVersionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * The filter to search for the ProductVersion to update in case it exists.
     */
    where: ProductVersionWhereUniqueInput
    /**
     * In case the ProductVersion found by the `where` argument doesn't exist, create a new ProductVersion with this data.
     */
    create: XOR<ProductVersionCreateInput, ProductVersionUncheckedCreateInput>
    /**
     * In case the ProductVersion was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ProductVersionUpdateInput, ProductVersionUncheckedUpdateInput>
  }

  /**
   * ProductVersion delete
   */
  export type ProductVersionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
    /**
     * Filter which ProductVersion to delete.
     */
    where: ProductVersionWhereUniqueInput
  }

  /**
   * ProductVersion deleteMany
   */
  export type ProductVersionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ProductVersions to delete
     */
    where?: ProductVersionWhereInput
    /**
     * Limit how many ProductVersions to delete.
     */
    limit?: number
  }

  /**
   * ProductVersion.licenses
   */
  export type ProductVersion$licensesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    where?: LicenseWhereInput
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    cursor?: LicenseWhereUniqueInput
    take?: number
    skip?: number
    distinct?: LicenseScalarFieldEnum | LicenseScalarFieldEnum[]
  }

  /**
   * ProductVersion.authorizations
   */
  export type ProductVersion$authorizationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    where?: AuthorizationWhereInput
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    cursor?: AuthorizationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AuthorizationScalarFieldEnum | AuthorizationScalarFieldEnum[]
  }

  /**
   * ProductVersion without action
   */
  export type ProductVersionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductVersion
     */
    select?: ProductVersionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductVersion
     */
    omit?: ProductVersionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductVersionInclude<ExtArgs> | null
  }


  /**
   * Model Authorization
   */

  export type AggregateAuthorization = {
    _count: AuthorizationCountAggregateOutputType | null
    _avg: AuthorizationAvgAggregateOutputType | null
    _sum: AuthorizationSumAggregateOutputType | null
    _min: AuthorizationMinAggregateOutputType | null
    _max: AuthorizationMaxAggregateOutputType | null
  }

  export type AuthorizationAvgAggregateOutputType = {
    id: number | null
    distributorId: number | null
    versionId: number | null
    customPrice: number | null
  }

  export type AuthorizationSumAggregateOutputType = {
    id: number | null
    distributorId: number | null
    versionId: number | null
    customPrice: number | null
  }

  export type AuthorizationMinAggregateOutputType = {
    id: number | null
    distributorId: number | null
    versionId: number | null
    customPrice: number | null
    status: $Enums.AuthorizationStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AuthorizationMaxAggregateOutputType = {
    id: number | null
    distributorId: number | null
    versionId: number | null
    customPrice: number | null
    status: $Enums.AuthorizationStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AuthorizationCountAggregateOutputType = {
    id: number
    distributorId: number
    versionId: number
    customPrice: number
    status: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AuthorizationAvgAggregateInputType = {
    id?: true
    distributorId?: true
    versionId?: true
    customPrice?: true
  }

  export type AuthorizationSumAggregateInputType = {
    id?: true
    distributorId?: true
    versionId?: true
    customPrice?: true
  }

  export type AuthorizationMinAggregateInputType = {
    id?: true
    distributorId?: true
    versionId?: true
    customPrice?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AuthorizationMaxAggregateInputType = {
    id?: true
    distributorId?: true
    versionId?: true
    customPrice?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AuthorizationCountAggregateInputType = {
    id?: true
    distributorId?: true
    versionId?: true
    customPrice?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AuthorizationAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Authorization to aggregate.
     */
    where?: AuthorizationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Authorizations to fetch.
     */
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AuthorizationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Authorizations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Authorizations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Authorizations
    **/
    _count?: true | AuthorizationCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AuthorizationAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AuthorizationSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AuthorizationMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AuthorizationMaxAggregateInputType
  }

  export type GetAuthorizationAggregateType<T extends AuthorizationAggregateArgs> = {
        [P in keyof T & keyof AggregateAuthorization]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAuthorization[P]>
      : GetScalarType<T[P], AggregateAuthorization[P]>
  }




  export type AuthorizationGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AuthorizationWhereInput
    orderBy?: AuthorizationOrderByWithAggregationInput | AuthorizationOrderByWithAggregationInput[]
    by: AuthorizationScalarFieldEnum[] | AuthorizationScalarFieldEnum
    having?: AuthorizationScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AuthorizationCountAggregateInputType | true
    _avg?: AuthorizationAvgAggregateInputType
    _sum?: AuthorizationSumAggregateInputType
    _min?: AuthorizationMinAggregateInputType
    _max?: AuthorizationMaxAggregateInputType
  }

  export type AuthorizationGroupByOutputType = {
    id: number
    distributorId: number
    versionId: number
    customPrice: number | null
    status: $Enums.AuthorizationStatus
    createdAt: Date
    updatedAt: Date
    _count: AuthorizationCountAggregateOutputType | null
    _avg: AuthorizationAvgAggregateOutputType | null
    _sum: AuthorizationSumAggregateOutputType | null
    _min: AuthorizationMinAggregateOutputType | null
    _max: AuthorizationMaxAggregateOutputType | null
  }

  type GetAuthorizationGroupByPayload<T extends AuthorizationGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AuthorizationGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AuthorizationGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AuthorizationGroupByOutputType[P]>
            : GetScalarType<T[P], AuthorizationGroupByOutputType[P]>
        }
      >
    >


  export type AuthorizationSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    distributorId?: boolean
    versionId?: boolean
    customPrice?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["authorization"]>

  export type AuthorizationSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    distributorId?: boolean
    versionId?: boolean
    customPrice?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["authorization"]>

  export type AuthorizationSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    distributorId?: boolean
    versionId?: boolean
    customPrice?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["authorization"]>

  export type AuthorizationSelectScalar = {
    id?: boolean
    distributorId?: boolean
    versionId?: boolean
    customPrice?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AuthorizationOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "distributorId" | "versionId" | "customPrice" | "status" | "createdAt" | "updatedAt", ExtArgs["result"]["authorization"]>
  export type AuthorizationInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }
  export type AuthorizationIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }
  export type AuthorizationIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    distributor?: boolean | UserDefaultArgs<ExtArgs>
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
  }

  export type $AuthorizationPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Authorization"
    objects: {
      distributor: Prisma.$UserPayload<ExtArgs>
      version: Prisma.$ProductVersionPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      distributorId: number
      versionId: number
      customPrice: number | null
      status: $Enums.AuthorizationStatus
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["authorization"]>
    composites: {}
  }

  type AuthorizationGetPayload<S extends boolean | null | undefined | AuthorizationDefaultArgs> = $Result.GetResult<Prisma.$AuthorizationPayload, S>

  type AuthorizationCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AuthorizationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AuthorizationCountAggregateInputType | true
    }

  export interface AuthorizationDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Authorization'], meta: { name: 'Authorization' } }
    /**
     * Find zero or one Authorization that matches the filter.
     * @param {AuthorizationFindUniqueArgs} args - Arguments to find a Authorization
     * @example
     * // Get one Authorization
     * const authorization = await prisma.authorization.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AuthorizationFindUniqueArgs>(args: SelectSubset<T, AuthorizationFindUniqueArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Authorization that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AuthorizationFindUniqueOrThrowArgs} args - Arguments to find a Authorization
     * @example
     * // Get one Authorization
     * const authorization = await prisma.authorization.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AuthorizationFindUniqueOrThrowArgs>(args: SelectSubset<T, AuthorizationFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Authorization that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationFindFirstArgs} args - Arguments to find a Authorization
     * @example
     * // Get one Authorization
     * const authorization = await prisma.authorization.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AuthorizationFindFirstArgs>(args?: SelectSubset<T, AuthorizationFindFirstArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Authorization that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationFindFirstOrThrowArgs} args - Arguments to find a Authorization
     * @example
     * // Get one Authorization
     * const authorization = await prisma.authorization.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AuthorizationFindFirstOrThrowArgs>(args?: SelectSubset<T, AuthorizationFindFirstOrThrowArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Authorizations that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Authorizations
     * const authorizations = await prisma.authorization.findMany()
     * 
     * // Get first 10 Authorizations
     * const authorizations = await prisma.authorization.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const authorizationWithIdOnly = await prisma.authorization.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AuthorizationFindManyArgs>(args?: SelectSubset<T, AuthorizationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Authorization.
     * @param {AuthorizationCreateArgs} args - Arguments to create a Authorization.
     * @example
     * // Create one Authorization
     * const Authorization = await prisma.authorization.create({
     *   data: {
     *     // ... data to create a Authorization
     *   }
     * })
     * 
     */
    create<T extends AuthorizationCreateArgs>(args: SelectSubset<T, AuthorizationCreateArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Authorizations.
     * @param {AuthorizationCreateManyArgs} args - Arguments to create many Authorizations.
     * @example
     * // Create many Authorizations
     * const authorization = await prisma.authorization.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AuthorizationCreateManyArgs>(args?: SelectSubset<T, AuthorizationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Authorizations and returns the data saved in the database.
     * @param {AuthorizationCreateManyAndReturnArgs} args - Arguments to create many Authorizations.
     * @example
     * // Create many Authorizations
     * const authorization = await prisma.authorization.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Authorizations and only return the `id`
     * const authorizationWithIdOnly = await prisma.authorization.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AuthorizationCreateManyAndReturnArgs>(args?: SelectSubset<T, AuthorizationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Authorization.
     * @param {AuthorizationDeleteArgs} args - Arguments to delete one Authorization.
     * @example
     * // Delete one Authorization
     * const Authorization = await prisma.authorization.delete({
     *   where: {
     *     // ... filter to delete one Authorization
     *   }
     * })
     * 
     */
    delete<T extends AuthorizationDeleteArgs>(args: SelectSubset<T, AuthorizationDeleteArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Authorization.
     * @param {AuthorizationUpdateArgs} args - Arguments to update one Authorization.
     * @example
     * // Update one Authorization
     * const authorization = await prisma.authorization.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AuthorizationUpdateArgs>(args: SelectSubset<T, AuthorizationUpdateArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Authorizations.
     * @param {AuthorizationDeleteManyArgs} args - Arguments to filter Authorizations to delete.
     * @example
     * // Delete a few Authorizations
     * const { count } = await prisma.authorization.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AuthorizationDeleteManyArgs>(args?: SelectSubset<T, AuthorizationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Authorizations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Authorizations
     * const authorization = await prisma.authorization.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AuthorizationUpdateManyArgs>(args: SelectSubset<T, AuthorizationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Authorizations and returns the data updated in the database.
     * @param {AuthorizationUpdateManyAndReturnArgs} args - Arguments to update many Authorizations.
     * @example
     * // Update many Authorizations
     * const authorization = await prisma.authorization.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Authorizations and only return the `id`
     * const authorizationWithIdOnly = await prisma.authorization.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AuthorizationUpdateManyAndReturnArgs>(args: SelectSubset<T, AuthorizationUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Authorization.
     * @param {AuthorizationUpsertArgs} args - Arguments to update or create a Authorization.
     * @example
     * // Update or create a Authorization
     * const authorization = await prisma.authorization.upsert({
     *   create: {
     *     // ... data to create a Authorization
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Authorization we want to update
     *   }
     * })
     */
    upsert<T extends AuthorizationUpsertArgs>(args: SelectSubset<T, AuthorizationUpsertArgs<ExtArgs>>): Prisma__AuthorizationClient<$Result.GetResult<Prisma.$AuthorizationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Authorizations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationCountArgs} args - Arguments to filter Authorizations to count.
     * @example
     * // Count the number of Authorizations
     * const count = await prisma.authorization.count({
     *   where: {
     *     // ... the filter for the Authorizations we want to count
     *   }
     * })
    **/
    count<T extends AuthorizationCountArgs>(
      args?: Subset<T, AuthorizationCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AuthorizationCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Authorization.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AuthorizationAggregateArgs>(args: Subset<T, AuthorizationAggregateArgs>): Prisma.PrismaPromise<GetAuthorizationAggregateType<T>>

    /**
     * Group by Authorization.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuthorizationGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AuthorizationGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AuthorizationGroupByArgs['orderBy'] }
        : { orderBy?: AuthorizationGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AuthorizationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAuthorizationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Authorization model
   */
  readonly fields: AuthorizationFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Authorization.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AuthorizationClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    distributor<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    version<T extends ProductVersionDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ProductVersionDefaultArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Authorization model
   */
  interface AuthorizationFieldRefs {
    readonly id: FieldRef<"Authorization", 'Int'>
    readonly distributorId: FieldRef<"Authorization", 'Int'>
    readonly versionId: FieldRef<"Authorization", 'Int'>
    readonly customPrice: FieldRef<"Authorization", 'Float'>
    readonly status: FieldRef<"Authorization", 'AuthorizationStatus'>
    readonly createdAt: FieldRef<"Authorization", 'DateTime'>
    readonly updatedAt: FieldRef<"Authorization", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Authorization findUnique
   */
  export type AuthorizationFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter, which Authorization to fetch.
     */
    where: AuthorizationWhereUniqueInput
  }

  /**
   * Authorization findUniqueOrThrow
   */
  export type AuthorizationFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter, which Authorization to fetch.
     */
    where: AuthorizationWhereUniqueInput
  }

  /**
   * Authorization findFirst
   */
  export type AuthorizationFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter, which Authorization to fetch.
     */
    where?: AuthorizationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Authorizations to fetch.
     */
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Authorizations.
     */
    cursor?: AuthorizationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Authorizations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Authorizations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Authorizations.
     */
    distinct?: AuthorizationScalarFieldEnum | AuthorizationScalarFieldEnum[]
  }

  /**
   * Authorization findFirstOrThrow
   */
  export type AuthorizationFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter, which Authorization to fetch.
     */
    where?: AuthorizationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Authorizations to fetch.
     */
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Authorizations.
     */
    cursor?: AuthorizationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Authorizations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Authorizations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Authorizations.
     */
    distinct?: AuthorizationScalarFieldEnum | AuthorizationScalarFieldEnum[]
  }

  /**
   * Authorization findMany
   */
  export type AuthorizationFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter, which Authorizations to fetch.
     */
    where?: AuthorizationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Authorizations to fetch.
     */
    orderBy?: AuthorizationOrderByWithRelationInput | AuthorizationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Authorizations.
     */
    cursor?: AuthorizationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Authorizations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Authorizations.
     */
    skip?: number
    distinct?: AuthorizationScalarFieldEnum | AuthorizationScalarFieldEnum[]
  }

  /**
   * Authorization create
   */
  export type AuthorizationCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * The data needed to create a Authorization.
     */
    data: XOR<AuthorizationCreateInput, AuthorizationUncheckedCreateInput>
  }

  /**
   * Authorization createMany
   */
  export type AuthorizationCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Authorizations.
     */
    data: AuthorizationCreateManyInput | AuthorizationCreateManyInput[]
  }

  /**
   * Authorization createManyAndReturn
   */
  export type AuthorizationCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * The data used to create many Authorizations.
     */
    data: AuthorizationCreateManyInput | AuthorizationCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Authorization update
   */
  export type AuthorizationUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * The data needed to update a Authorization.
     */
    data: XOR<AuthorizationUpdateInput, AuthorizationUncheckedUpdateInput>
    /**
     * Choose, which Authorization to update.
     */
    where: AuthorizationWhereUniqueInput
  }

  /**
   * Authorization updateMany
   */
  export type AuthorizationUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Authorizations.
     */
    data: XOR<AuthorizationUpdateManyMutationInput, AuthorizationUncheckedUpdateManyInput>
    /**
     * Filter which Authorizations to update
     */
    where?: AuthorizationWhereInput
    /**
     * Limit how many Authorizations to update.
     */
    limit?: number
  }

  /**
   * Authorization updateManyAndReturn
   */
  export type AuthorizationUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * The data used to update Authorizations.
     */
    data: XOR<AuthorizationUpdateManyMutationInput, AuthorizationUncheckedUpdateManyInput>
    /**
     * Filter which Authorizations to update
     */
    where?: AuthorizationWhereInput
    /**
     * Limit how many Authorizations to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Authorization upsert
   */
  export type AuthorizationUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * The filter to search for the Authorization to update in case it exists.
     */
    where: AuthorizationWhereUniqueInput
    /**
     * In case the Authorization found by the `where` argument doesn't exist, create a new Authorization with this data.
     */
    create: XOR<AuthorizationCreateInput, AuthorizationUncheckedCreateInput>
    /**
     * In case the Authorization was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AuthorizationUpdateInput, AuthorizationUncheckedUpdateInput>
  }

  /**
   * Authorization delete
   */
  export type AuthorizationDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
    /**
     * Filter which Authorization to delete.
     */
    where: AuthorizationWhereUniqueInput
  }

  /**
   * Authorization deleteMany
   */
  export type AuthorizationDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Authorizations to delete
     */
    where?: AuthorizationWhereInput
    /**
     * Limit how many Authorizations to delete.
     */
    limit?: number
  }

  /**
   * Authorization without action
   */
  export type AuthorizationDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Authorization
     */
    select?: AuthorizationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Authorization
     */
    omit?: AuthorizationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuthorizationInclude<ExtArgs> | null
  }


  /**
   * Model License
   */

  export type AggregateLicense = {
    _count: LicenseCountAggregateOutputType | null
    _avg: LicenseAvgAggregateOutputType | null
    _sum: LicenseSumAggregateOutputType | null
    _min: LicenseMinAggregateOutputType | null
    _max: LicenseMaxAggregateOutputType | null
  }

  export type LicenseAvgAggregateOutputType = {
    id: number | null
    versionId: number | null
    distributorId: number | null
  }

  export type LicenseSumAggregateOutputType = {
    id: number | null
    versionId: number | null
    distributorId: number | null
  }

  export type LicenseMinAggregateOutputType = {
    id: number | null
    versionId: number | null
    licenseKey: string | null
    status: $Enums.LicenseStatus | null
    verifyInstance: string | null
    activatedAt: Date | null
    distributorId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type LicenseMaxAggregateOutputType = {
    id: number | null
    versionId: number | null
    licenseKey: string | null
    status: $Enums.LicenseStatus | null
    verifyInstance: string | null
    activatedAt: Date | null
    distributorId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type LicenseCountAggregateOutputType = {
    id: number
    versionId: number
    licenseKey: number
    status: number
    verifyInstance: number
    activatedAt: number
    distributorId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type LicenseAvgAggregateInputType = {
    id?: true
    versionId?: true
    distributorId?: true
  }

  export type LicenseSumAggregateInputType = {
    id?: true
    versionId?: true
    distributorId?: true
  }

  export type LicenseMinAggregateInputType = {
    id?: true
    versionId?: true
    licenseKey?: true
    status?: true
    verifyInstance?: true
    activatedAt?: true
    distributorId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type LicenseMaxAggregateInputType = {
    id?: true
    versionId?: true
    licenseKey?: true
    status?: true
    verifyInstance?: true
    activatedAt?: true
    distributorId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type LicenseCountAggregateInputType = {
    id?: true
    versionId?: true
    licenseKey?: true
    status?: true
    verifyInstance?: true
    activatedAt?: true
    distributorId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type LicenseAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which License to aggregate.
     */
    where?: LicenseWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Licenses to fetch.
     */
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: LicenseWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Licenses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Licenses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Licenses
    **/
    _count?: true | LicenseCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: LicenseAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: LicenseSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: LicenseMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: LicenseMaxAggregateInputType
  }

  export type GetLicenseAggregateType<T extends LicenseAggregateArgs> = {
        [P in keyof T & keyof AggregateLicense]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateLicense[P]>
      : GetScalarType<T[P], AggregateLicense[P]>
  }




  export type LicenseGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: LicenseWhereInput
    orderBy?: LicenseOrderByWithAggregationInput | LicenseOrderByWithAggregationInput[]
    by: LicenseScalarFieldEnum[] | LicenseScalarFieldEnum
    having?: LicenseScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: LicenseCountAggregateInputType | true
    _avg?: LicenseAvgAggregateInputType
    _sum?: LicenseSumAggregateInputType
    _min?: LicenseMinAggregateInputType
    _max?: LicenseMaxAggregateInputType
  }

  export type LicenseGroupByOutputType = {
    id: number
    versionId: number
    licenseKey: string
    status: $Enums.LicenseStatus
    verifyInstance: string | null
    activatedAt: Date | null
    distributorId: number
    createdAt: Date
    updatedAt: Date
    _count: LicenseCountAggregateOutputType | null
    _avg: LicenseAvgAggregateOutputType | null
    _sum: LicenseSumAggregateOutputType | null
    _min: LicenseMinAggregateOutputType | null
    _max: LicenseMaxAggregateOutputType | null
  }

  type GetLicenseGroupByPayload<T extends LicenseGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<LicenseGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof LicenseGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], LicenseGroupByOutputType[P]>
            : GetScalarType<T[P], LicenseGroupByOutputType[P]>
        }
      >
    >


  export type LicenseSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    versionId?: boolean
    licenseKey?: boolean
    status?: boolean
    verifyInstance?: boolean
    activatedAt?: boolean
    distributorId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["license"]>

  export type LicenseSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    versionId?: boolean
    licenseKey?: boolean
    status?: boolean
    verifyInstance?: boolean
    activatedAt?: boolean
    distributorId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["license"]>

  export type LicenseSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    versionId?: boolean
    licenseKey?: boolean
    status?: boolean
    verifyInstance?: boolean
    activatedAt?: boolean
    distributorId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["license"]>

  export type LicenseSelectScalar = {
    id?: boolean
    versionId?: boolean
    licenseKey?: boolean
    status?: boolean
    verifyInstance?: boolean
    activatedAt?: boolean
    distributorId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type LicenseOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "versionId" | "licenseKey" | "status" | "verifyInstance" | "activatedAt" | "distributorId" | "createdAt" | "updatedAt", ExtArgs["result"]["license"]>
  export type LicenseInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type LicenseIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type LicenseIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    version?: boolean | ProductVersionDefaultArgs<ExtArgs>
    distributor?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $LicensePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "License"
    objects: {
      version: Prisma.$ProductVersionPayload<ExtArgs>
      distributor: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      versionId: number
      licenseKey: string
      status: $Enums.LicenseStatus
      verifyInstance: string | null
      activatedAt: Date | null
      distributorId: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["license"]>
    composites: {}
  }

  type LicenseGetPayload<S extends boolean | null | undefined | LicenseDefaultArgs> = $Result.GetResult<Prisma.$LicensePayload, S>

  type LicenseCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<LicenseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: LicenseCountAggregateInputType | true
    }

  export interface LicenseDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['License'], meta: { name: 'License' } }
    /**
     * Find zero or one License that matches the filter.
     * @param {LicenseFindUniqueArgs} args - Arguments to find a License
     * @example
     * // Get one License
     * const license = await prisma.license.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends LicenseFindUniqueArgs>(args: SelectSubset<T, LicenseFindUniqueArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one License that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {LicenseFindUniqueOrThrowArgs} args - Arguments to find a License
     * @example
     * // Get one License
     * const license = await prisma.license.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends LicenseFindUniqueOrThrowArgs>(args: SelectSubset<T, LicenseFindUniqueOrThrowArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first License that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseFindFirstArgs} args - Arguments to find a License
     * @example
     * // Get one License
     * const license = await prisma.license.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends LicenseFindFirstArgs>(args?: SelectSubset<T, LicenseFindFirstArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first License that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseFindFirstOrThrowArgs} args - Arguments to find a License
     * @example
     * // Get one License
     * const license = await prisma.license.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends LicenseFindFirstOrThrowArgs>(args?: SelectSubset<T, LicenseFindFirstOrThrowArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Licenses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Licenses
     * const licenses = await prisma.license.findMany()
     * 
     * // Get first 10 Licenses
     * const licenses = await prisma.license.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const licenseWithIdOnly = await prisma.license.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends LicenseFindManyArgs>(args?: SelectSubset<T, LicenseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a License.
     * @param {LicenseCreateArgs} args - Arguments to create a License.
     * @example
     * // Create one License
     * const License = await prisma.license.create({
     *   data: {
     *     // ... data to create a License
     *   }
     * })
     * 
     */
    create<T extends LicenseCreateArgs>(args: SelectSubset<T, LicenseCreateArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Licenses.
     * @param {LicenseCreateManyArgs} args - Arguments to create many Licenses.
     * @example
     * // Create many Licenses
     * const license = await prisma.license.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends LicenseCreateManyArgs>(args?: SelectSubset<T, LicenseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Licenses and returns the data saved in the database.
     * @param {LicenseCreateManyAndReturnArgs} args - Arguments to create many Licenses.
     * @example
     * // Create many Licenses
     * const license = await prisma.license.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Licenses and only return the `id`
     * const licenseWithIdOnly = await prisma.license.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends LicenseCreateManyAndReturnArgs>(args?: SelectSubset<T, LicenseCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a License.
     * @param {LicenseDeleteArgs} args - Arguments to delete one License.
     * @example
     * // Delete one License
     * const License = await prisma.license.delete({
     *   where: {
     *     // ... filter to delete one License
     *   }
     * })
     * 
     */
    delete<T extends LicenseDeleteArgs>(args: SelectSubset<T, LicenseDeleteArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one License.
     * @param {LicenseUpdateArgs} args - Arguments to update one License.
     * @example
     * // Update one License
     * const license = await prisma.license.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends LicenseUpdateArgs>(args: SelectSubset<T, LicenseUpdateArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Licenses.
     * @param {LicenseDeleteManyArgs} args - Arguments to filter Licenses to delete.
     * @example
     * // Delete a few Licenses
     * const { count } = await prisma.license.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends LicenseDeleteManyArgs>(args?: SelectSubset<T, LicenseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Licenses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Licenses
     * const license = await prisma.license.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends LicenseUpdateManyArgs>(args: SelectSubset<T, LicenseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Licenses and returns the data updated in the database.
     * @param {LicenseUpdateManyAndReturnArgs} args - Arguments to update many Licenses.
     * @example
     * // Update many Licenses
     * const license = await prisma.license.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Licenses and only return the `id`
     * const licenseWithIdOnly = await prisma.license.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends LicenseUpdateManyAndReturnArgs>(args: SelectSubset<T, LicenseUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one License.
     * @param {LicenseUpsertArgs} args - Arguments to update or create a License.
     * @example
     * // Update or create a License
     * const license = await prisma.license.upsert({
     *   create: {
     *     // ... data to create a License
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the License we want to update
     *   }
     * })
     */
    upsert<T extends LicenseUpsertArgs>(args: SelectSubset<T, LicenseUpsertArgs<ExtArgs>>): Prisma__LicenseClient<$Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Licenses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseCountArgs} args - Arguments to filter Licenses to count.
     * @example
     * // Count the number of Licenses
     * const count = await prisma.license.count({
     *   where: {
     *     // ... the filter for the Licenses we want to count
     *   }
     * })
    **/
    count<T extends LicenseCountArgs>(
      args?: Subset<T, LicenseCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], LicenseCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a License.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends LicenseAggregateArgs>(args: Subset<T, LicenseAggregateArgs>): Prisma.PrismaPromise<GetLicenseAggregateType<T>>

    /**
     * Group by License.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {LicenseGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends LicenseGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: LicenseGroupByArgs['orderBy'] }
        : { orderBy?: LicenseGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, LicenseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLicenseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the License model
   */
  readonly fields: LicenseFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for License.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__LicenseClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    version<T extends ProductVersionDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ProductVersionDefaultArgs<ExtArgs>>): Prisma__ProductVersionClient<$Result.GetResult<Prisma.$ProductVersionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    distributor<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the License model
   */
  interface LicenseFieldRefs {
    readonly id: FieldRef<"License", 'Int'>
    readonly versionId: FieldRef<"License", 'Int'>
    readonly licenseKey: FieldRef<"License", 'String'>
    readonly status: FieldRef<"License", 'LicenseStatus'>
    readonly verifyInstance: FieldRef<"License", 'String'>
    readonly activatedAt: FieldRef<"License", 'DateTime'>
    readonly distributorId: FieldRef<"License", 'Int'>
    readonly createdAt: FieldRef<"License", 'DateTime'>
    readonly updatedAt: FieldRef<"License", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * License findUnique
   */
  export type LicenseFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter, which License to fetch.
     */
    where: LicenseWhereUniqueInput
  }

  /**
   * License findUniqueOrThrow
   */
  export type LicenseFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter, which License to fetch.
     */
    where: LicenseWhereUniqueInput
  }

  /**
   * License findFirst
   */
  export type LicenseFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter, which License to fetch.
     */
    where?: LicenseWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Licenses to fetch.
     */
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Licenses.
     */
    cursor?: LicenseWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Licenses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Licenses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Licenses.
     */
    distinct?: LicenseScalarFieldEnum | LicenseScalarFieldEnum[]
  }

  /**
   * License findFirstOrThrow
   */
  export type LicenseFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter, which License to fetch.
     */
    where?: LicenseWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Licenses to fetch.
     */
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Licenses.
     */
    cursor?: LicenseWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Licenses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Licenses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Licenses.
     */
    distinct?: LicenseScalarFieldEnum | LicenseScalarFieldEnum[]
  }

  /**
   * License findMany
   */
  export type LicenseFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter, which Licenses to fetch.
     */
    where?: LicenseWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Licenses to fetch.
     */
    orderBy?: LicenseOrderByWithRelationInput | LicenseOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Licenses.
     */
    cursor?: LicenseWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Licenses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Licenses.
     */
    skip?: number
    distinct?: LicenseScalarFieldEnum | LicenseScalarFieldEnum[]
  }

  /**
   * License create
   */
  export type LicenseCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * The data needed to create a License.
     */
    data: XOR<LicenseCreateInput, LicenseUncheckedCreateInput>
  }

  /**
   * License createMany
   */
  export type LicenseCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Licenses.
     */
    data: LicenseCreateManyInput | LicenseCreateManyInput[]
  }

  /**
   * License createManyAndReturn
   */
  export type LicenseCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * The data used to create many Licenses.
     */
    data: LicenseCreateManyInput | LicenseCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * License update
   */
  export type LicenseUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * The data needed to update a License.
     */
    data: XOR<LicenseUpdateInput, LicenseUncheckedUpdateInput>
    /**
     * Choose, which License to update.
     */
    where: LicenseWhereUniqueInput
  }

  /**
   * License updateMany
   */
  export type LicenseUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Licenses.
     */
    data: XOR<LicenseUpdateManyMutationInput, LicenseUncheckedUpdateManyInput>
    /**
     * Filter which Licenses to update
     */
    where?: LicenseWhereInput
    /**
     * Limit how many Licenses to update.
     */
    limit?: number
  }

  /**
   * License updateManyAndReturn
   */
  export type LicenseUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * The data used to update Licenses.
     */
    data: XOR<LicenseUpdateManyMutationInput, LicenseUncheckedUpdateManyInput>
    /**
     * Filter which Licenses to update
     */
    where?: LicenseWhereInput
    /**
     * Limit how many Licenses to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * License upsert
   */
  export type LicenseUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * The filter to search for the License to update in case it exists.
     */
    where: LicenseWhereUniqueInput
    /**
     * In case the License found by the `where` argument doesn't exist, create a new License with this data.
     */
    create: XOR<LicenseCreateInput, LicenseUncheckedCreateInput>
    /**
     * In case the License was found with the provided `where` argument, update it with this data.
     */
    update: XOR<LicenseUpdateInput, LicenseUncheckedUpdateInput>
  }

  /**
   * License delete
   */
  export type LicenseDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
    /**
     * Filter which License to delete.
     */
    where: LicenseWhereUniqueInput
  }

  /**
   * License deleteMany
   */
  export type LicenseDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Licenses to delete
     */
    where?: LicenseWhereInput
    /**
     * Limit how many Licenses to delete.
     */
    limit?: number
  }

  /**
   * License without action
   */
  export type LicenseDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the License
     */
    select?: LicenseSelect<ExtArgs> | null
    /**
     * Omit specific fields from the License
     */
    omit?: LicenseOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: LicenseInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    username: 'username',
    passwordHash: 'passwordHash',
    role: 'role',
    status: 'status',
    nickName: 'nickName',
    wechat: 'wechat',
    avatar: 'avatar',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const ProductScalarFieldEnum: {
    id: 'id',
    name: 'name',
    description: 'description',
    category: 'category',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ProductScalarFieldEnum = (typeof ProductScalarFieldEnum)[keyof typeof ProductScalarFieldEnum]


  export const ProductVersionScalarFieldEnum: {
    id: 'id',
    productId: 'productId',
    version: 'version',
    versionName: 'versionName',
    description: 'description',
    verifyTemplate: 'verifyTemplate',
    encryptionKey: 'encryptionKey',
    defaultPrice: 'defaultPrice',
    downloadLink: 'downloadLink',
    coverUrl: 'coverUrl',
    changelog: 'changelog',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ProductVersionScalarFieldEnum = (typeof ProductVersionScalarFieldEnum)[keyof typeof ProductVersionScalarFieldEnum]


  export const AuthorizationScalarFieldEnum: {
    id: 'id',
    distributorId: 'distributorId',
    versionId: 'versionId',
    customPrice: 'customPrice',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AuthorizationScalarFieldEnum = (typeof AuthorizationScalarFieldEnum)[keyof typeof AuthorizationScalarFieldEnum]


  export const LicenseScalarFieldEnum: {
    id: 'id',
    versionId: 'versionId',
    licenseKey: 'licenseKey',
    status: 'status',
    verifyInstance: 'verifyInstance',
    activatedAt: 'activatedAt',
    distributorId: 'distributorId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type LicenseScalarFieldEnum = (typeof LicenseScalarFieldEnum)[keyof typeof LicenseScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'UserRole'
   */
  export type EnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole'>
    


  /**
   * Reference to a field of type 'UserStatus'
   */
  export type EnumUserStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserStatus'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'ProductStatus'
   */
  export type EnumProductStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ProductStatus'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'AuthorizationStatus'
   */
  export type EnumAuthorizationStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'AuthorizationStatus'>
    


  /**
   * Reference to a field of type 'LicenseStatus'
   */
  export type EnumLicenseStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'LicenseStatus'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: IntFilter<"User"> | number
    username?: StringFilter<"User"> | string
    passwordHash?: StringFilter<"User"> | string
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    status?: EnumUserStatusFilter<"User"> | $Enums.UserStatus
    nickName?: StringNullableFilter<"User"> | string | null
    wechat?: StringNullableFilter<"User"> | string | null
    avatar?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    licenses?: LicenseListRelationFilter
    authorizations?: AuthorizationListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    username?: SortOrder
    passwordHash?: SortOrder
    role?: SortOrder
    status?: SortOrder
    nickName?: SortOrderInput | SortOrder
    wechat?: SortOrderInput | SortOrder
    avatar?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    licenses?: LicenseOrderByRelationAggregateInput
    authorizations?: AuthorizationOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    username?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    passwordHash?: StringFilter<"User"> | string
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    status?: EnumUserStatusFilter<"User"> | $Enums.UserStatus
    nickName?: StringNullableFilter<"User"> | string | null
    wechat?: StringNullableFilter<"User"> | string | null
    avatar?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    licenses?: LicenseListRelationFilter
    authorizations?: AuthorizationListRelationFilter
  }, "id" | "username">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    username?: SortOrder
    passwordHash?: SortOrder
    role?: SortOrder
    status?: SortOrder
    nickName?: SortOrderInput | SortOrder
    wechat?: SortOrderInput | SortOrder
    avatar?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"User"> | number
    username?: StringWithAggregatesFilter<"User"> | string
    passwordHash?: StringWithAggregatesFilter<"User"> | string
    role?: EnumUserRoleWithAggregatesFilter<"User"> | $Enums.UserRole
    status?: EnumUserStatusWithAggregatesFilter<"User"> | $Enums.UserStatus
    nickName?: StringNullableWithAggregatesFilter<"User"> | string | null
    wechat?: StringNullableWithAggregatesFilter<"User"> | string | null
    avatar?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type ProductWhereInput = {
    AND?: ProductWhereInput | ProductWhereInput[]
    OR?: ProductWhereInput[]
    NOT?: ProductWhereInput | ProductWhereInput[]
    id?: IntFilter<"Product"> | number
    name?: StringFilter<"Product"> | string
    description?: StringNullableFilter<"Product"> | string | null
    category?: StringNullableFilter<"Product"> | string | null
    status?: EnumProductStatusFilter<"Product"> | $Enums.ProductStatus
    createdAt?: DateTimeFilter<"Product"> | Date | string
    updatedAt?: DateTimeFilter<"Product"> | Date | string
    versions?: ProductVersionListRelationFilter
  }

  export type ProductOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    versions?: ProductVersionOrderByRelationAggregateInput
  }

  export type ProductWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    name?: string
    AND?: ProductWhereInput | ProductWhereInput[]
    OR?: ProductWhereInput[]
    NOT?: ProductWhereInput | ProductWhereInput[]
    description?: StringNullableFilter<"Product"> | string | null
    category?: StringNullableFilter<"Product"> | string | null
    status?: EnumProductStatusFilter<"Product"> | $Enums.ProductStatus
    createdAt?: DateTimeFilter<"Product"> | Date | string
    updatedAt?: DateTimeFilter<"Product"> | Date | string
    versions?: ProductVersionListRelationFilter
  }, "id" | "name">

  export type ProductOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ProductCountOrderByAggregateInput
    _avg?: ProductAvgOrderByAggregateInput
    _max?: ProductMaxOrderByAggregateInput
    _min?: ProductMinOrderByAggregateInput
    _sum?: ProductSumOrderByAggregateInput
  }

  export type ProductScalarWhereWithAggregatesInput = {
    AND?: ProductScalarWhereWithAggregatesInput | ProductScalarWhereWithAggregatesInput[]
    OR?: ProductScalarWhereWithAggregatesInput[]
    NOT?: ProductScalarWhereWithAggregatesInput | ProductScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Product"> | number
    name?: StringWithAggregatesFilter<"Product"> | string
    description?: StringNullableWithAggregatesFilter<"Product"> | string | null
    category?: StringNullableWithAggregatesFilter<"Product"> | string | null
    status?: EnumProductStatusWithAggregatesFilter<"Product"> | $Enums.ProductStatus
    createdAt?: DateTimeWithAggregatesFilter<"Product"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Product"> | Date | string
  }

  export type ProductVersionWhereInput = {
    AND?: ProductVersionWhereInput | ProductVersionWhereInput[]
    OR?: ProductVersionWhereInput[]
    NOT?: ProductVersionWhereInput | ProductVersionWhereInput[]
    id?: IntFilter<"ProductVersion"> | number
    productId?: IntFilter<"ProductVersion"> | number
    version?: StringFilter<"ProductVersion"> | string
    versionName?: StringNullableFilter<"ProductVersion"> | string | null
    description?: StringNullableFilter<"ProductVersion"> | string | null
    verifyTemplate?: StringFilter<"ProductVersion"> | string
    encryptionKey?: StringFilter<"ProductVersion"> | string
    defaultPrice?: FloatFilter<"ProductVersion"> | number
    downloadLink?: StringNullableFilter<"ProductVersion"> | string | null
    coverUrl?: StringNullableFilter<"ProductVersion"> | string | null
    changelog?: StringNullableFilter<"ProductVersion"> | string | null
    status?: EnumProductStatusFilter<"ProductVersion"> | $Enums.ProductStatus
    createdAt?: DateTimeFilter<"ProductVersion"> | Date | string
    updatedAt?: DateTimeFilter<"ProductVersion"> | Date | string
    product?: XOR<ProductScalarRelationFilter, ProductWhereInput>
    licenses?: LicenseListRelationFilter
    authorizations?: AuthorizationListRelationFilter
  }

  export type ProductVersionOrderByWithRelationInput = {
    id?: SortOrder
    productId?: SortOrder
    version?: SortOrder
    versionName?: SortOrderInput | SortOrder
    description?: SortOrderInput | SortOrder
    verifyTemplate?: SortOrder
    encryptionKey?: SortOrder
    defaultPrice?: SortOrder
    downloadLink?: SortOrderInput | SortOrder
    coverUrl?: SortOrderInput | SortOrder
    changelog?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    product?: ProductOrderByWithRelationInput
    licenses?: LicenseOrderByRelationAggregateInput
    authorizations?: AuthorizationOrderByRelationAggregateInput
  }

  export type ProductVersionWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    productId_version?: ProductVersionProductIdVersionCompoundUniqueInput
    AND?: ProductVersionWhereInput | ProductVersionWhereInput[]
    OR?: ProductVersionWhereInput[]
    NOT?: ProductVersionWhereInput | ProductVersionWhereInput[]
    productId?: IntFilter<"ProductVersion"> | number
    version?: StringFilter<"ProductVersion"> | string
    versionName?: StringNullableFilter<"ProductVersion"> | string | null
    description?: StringNullableFilter<"ProductVersion"> | string | null
    verifyTemplate?: StringFilter<"ProductVersion"> | string
    encryptionKey?: StringFilter<"ProductVersion"> | string
    defaultPrice?: FloatFilter<"ProductVersion"> | number
    downloadLink?: StringNullableFilter<"ProductVersion"> | string | null
    coverUrl?: StringNullableFilter<"ProductVersion"> | string | null
    changelog?: StringNullableFilter<"ProductVersion"> | string | null
    status?: EnumProductStatusFilter<"ProductVersion"> | $Enums.ProductStatus
    createdAt?: DateTimeFilter<"ProductVersion"> | Date | string
    updatedAt?: DateTimeFilter<"ProductVersion"> | Date | string
    product?: XOR<ProductScalarRelationFilter, ProductWhereInput>
    licenses?: LicenseListRelationFilter
    authorizations?: AuthorizationListRelationFilter
  }, "id" | "productId_version">

  export type ProductVersionOrderByWithAggregationInput = {
    id?: SortOrder
    productId?: SortOrder
    version?: SortOrder
    versionName?: SortOrderInput | SortOrder
    description?: SortOrderInput | SortOrder
    verifyTemplate?: SortOrder
    encryptionKey?: SortOrder
    defaultPrice?: SortOrder
    downloadLink?: SortOrderInput | SortOrder
    coverUrl?: SortOrderInput | SortOrder
    changelog?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ProductVersionCountOrderByAggregateInput
    _avg?: ProductVersionAvgOrderByAggregateInput
    _max?: ProductVersionMaxOrderByAggregateInput
    _min?: ProductVersionMinOrderByAggregateInput
    _sum?: ProductVersionSumOrderByAggregateInput
  }

  export type ProductVersionScalarWhereWithAggregatesInput = {
    AND?: ProductVersionScalarWhereWithAggregatesInput | ProductVersionScalarWhereWithAggregatesInput[]
    OR?: ProductVersionScalarWhereWithAggregatesInput[]
    NOT?: ProductVersionScalarWhereWithAggregatesInput | ProductVersionScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"ProductVersion"> | number
    productId?: IntWithAggregatesFilter<"ProductVersion"> | number
    version?: StringWithAggregatesFilter<"ProductVersion"> | string
    versionName?: StringNullableWithAggregatesFilter<"ProductVersion"> | string | null
    description?: StringNullableWithAggregatesFilter<"ProductVersion"> | string | null
    verifyTemplate?: StringWithAggregatesFilter<"ProductVersion"> | string
    encryptionKey?: StringWithAggregatesFilter<"ProductVersion"> | string
    defaultPrice?: FloatWithAggregatesFilter<"ProductVersion"> | number
    downloadLink?: StringNullableWithAggregatesFilter<"ProductVersion"> | string | null
    coverUrl?: StringNullableWithAggregatesFilter<"ProductVersion"> | string | null
    changelog?: StringNullableWithAggregatesFilter<"ProductVersion"> | string | null
    status?: EnumProductStatusWithAggregatesFilter<"ProductVersion"> | $Enums.ProductStatus
    createdAt?: DateTimeWithAggregatesFilter<"ProductVersion"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"ProductVersion"> | Date | string
  }

  export type AuthorizationWhereInput = {
    AND?: AuthorizationWhereInput | AuthorizationWhereInput[]
    OR?: AuthorizationWhereInput[]
    NOT?: AuthorizationWhereInput | AuthorizationWhereInput[]
    id?: IntFilter<"Authorization"> | number
    distributorId?: IntFilter<"Authorization"> | number
    versionId?: IntFilter<"Authorization"> | number
    customPrice?: FloatNullableFilter<"Authorization"> | number | null
    status?: EnumAuthorizationStatusFilter<"Authorization"> | $Enums.AuthorizationStatus
    createdAt?: DateTimeFilter<"Authorization"> | Date | string
    updatedAt?: DateTimeFilter<"Authorization"> | Date | string
    distributor?: XOR<UserScalarRelationFilter, UserWhereInput>
    version?: XOR<ProductVersionScalarRelationFilter, ProductVersionWhereInput>
  }

  export type AuthorizationOrderByWithRelationInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    distributor?: UserOrderByWithRelationInput
    version?: ProductVersionOrderByWithRelationInput
  }

  export type AuthorizationWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    distributorId_versionId?: AuthorizationDistributorIdVersionIdCompoundUniqueInput
    AND?: AuthorizationWhereInput | AuthorizationWhereInput[]
    OR?: AuthorizationWhereInput[]
    NOT?: AuthorizationWhereInput | AuthorizationWhereInput[]
    distributorId?: IntFilter<"Authorization"> | number
    versionId?: IntFilter<"Authorization"> | number
    customPrice?: FloatNullableFilter<"Authorization"> | number | null
    status?: EnumAuthorizationStatusFilter<"Authorization"> | $Enums.AuthorizationStatus
    createdAt?: DateTimeFilter<"Authorization"> | Date | string
    updatedAt?: DateTimeFilter<"Authorization"> | Date | string
    distributor?: XOR<UserScalarRelationFilter, UserWhereInput>
    version?: XOR<ProductVersionScalarRelationFilter, ProductVersionWhereInput>
  }, "id" | "distributorId_versionId">

  export type AuthorizationOrderByWithAggregationInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AuthorizationCountOrderByAggregateInput
    _avg?: AuthorizationAvgOrderByAggregateInput
    _max?: AuthorizationMaxOrderByAggregateInput
    _min?: AuthorizationMinOrderByAggregateInput
    _sum?: AuthorizationSumOrderByAggregateInput
  }

  export type AuthorizationScalarWhereWithAggregatesInput = {
    AND?: AuthorizationScalarWhereWithAggregatesInput | AuthorizationScalarWhereWithAggregatesInput[]
    OR?: AuthorizationScalarWhereWithAggregatesInput[]
    NOT?: AuthorizationScalarWhereWithAggregatesInput | AuthorizationScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Authorization"> | number
    distributorId?: IntWithAggregatesFilter<"Authorization"> | number
    versionId?: IntWithAggregatesFilter<"Authorization"> | number
    customPrice?: FloatNullableWithAggregatesFilter<"Authorization"> | number | null
    status?: EnumAuthorizationStatusWithAggregatesFilter<"Authorization"> | $Enums.AuthorizationStatus
    createdAt?: DateTimeWithAggregatesFilter<"Authorization"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Authorization"> | Date | string
  }

  export type LicenseWhereInput = {
    AND?: LicenseWhereInput | LicenseWhereInput[]
    OR?: LicenseWhereInput[]
    NOT?: LicenseWhereInput | LicenseWhereInput[]
    id?: IntFilter<"License"> | number
    versionId?: IntFilter<"License"> | number
    licenseKey?: StringFilter<"License"> | string
    status?: EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
    verifyInstance?: StringNullableFilter<"License"> | string | null
    activatedAt?: DateTimeNullableFilter<"License"> | Date | string | null
    distributorId?: IntFilter<"License"> | number
    createdAt?: DateTimeFilter<"License"> | Date | string
    updatedAt?: DateTimeFilter<"License"> | Date | string
    version?: XOR<ProductVersionScalarRelationFilter, ProductVersionWhereInput>
    distributor?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type LicenseOrderByWithRelationInput = {
    id?: SortOrder
    versionId?: SortOrder
    licenseKey?: SortOrder
    status?: SortOrder
    verifyInstance?: SortOrderInput | SortOrder
    activatedAt?: SortOrderInput | SortOrder
    distributorId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    version?: ProductVersionOrderByWithRelationInput
    distributor?: UserOrderByWithRelationInput
  }

  export type LicenseWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    licenseKey?: string
    AND?: LicenseWhereInput | LicenseWhereInput[]
    OR?: LicenseWhereInput[]
    NOT?: LicenseWhereInput | LicenseWhereInput[]
    versionId?: IntFilter<"License"> | number
    status?: EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
    verifyInstance?: StringNullableFilter<"License"> | string | null
    activatedAt?: DateTimeNullableFilter<"License"> | Date | string | null
    distributorId?: IntFilter<"License"> | number
    createdAt?: DateTimeFilter<"License"> | Date | string
    updatedAt?: DateTimeFilter<"License"> | Date | string
    version?: XOR<ProductVersionScalarRelationFilter, ProductVersionWhereInput>
    distributor?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id" | "licenseKey">

  export type LicenseOrderByWithAggregationInput = {
    id?: SortOrder
    versionId?: SortOrder
    licenseKey?: SortOrder
    status?: SortOrder
    verifyInstance?: SortOrderInput | SortOrder
    activatedAt?: SortOrderInput | SortOrder
    distributorId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: LicenseCountOrderByAggregateInput
    _avg?: LicenseAvgOrderByAggregateInput
    _max?: LicenseMaxOrderByAggregateInput
    _min?: LicenseMinOrderByAggregateInput
    _sum?: LicenseSumOrderByAggregateInput
  }

  export type LicenseScalarWhereWithAggregatesInput = {
    AND?: LicenseScalarWhereWithAggregatesInput | LicenseScalarWhereWithAggregatesInput[]
    OR?: LicenseScalarWhereWithAggregatesInput[]
    NOT?: LicenseScalarWhereWithAggregatesInput | LicenseScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"License"> | number
    versionId?: IntWithAggregatesFilter<"License"> | number
    licenseKey?: StringWithAggregatesFilter<"License"> | string
    status?: EnumLicenseStatusWithAggregatesFilter<"License"> | $Enums.LicenseStatus
    verifyInstance?: StringNullableWithAggregatesFilter<"License"> | string | null
    activatedAt?: DateTimeNullableWithAggregatesFilter<"License"> | Date | string | null
    distributorId?: IntWithAggregatesFilter<"License"> | number
    createdAt?: DateTimeWithAggregatesFilter<"License"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"License"> | Date | string
  }

  export type UserCreateInput = {
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseCreateNestedManyWithoutDistributorInput
    authorizations?: AuthorizationCreateNestedManyWithoutDistributorInput
  }

  export type UserUncheckedCreateInput = {
    id?: number
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseUncheckedCreateNestedManyWithoutDistributorInput
    authorizations?: AuthorizationUncheckedCreateNestedManyWithoutDistributorInput
  }

  export type UserUpdateInput = {
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUpdateManyWithoutDistributorNestedInput
    authorizations?: AuthorizationUpdateManyWithoutDistributorNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUncheckedUpdateManyWithoutDistributorNestedInput
    authorizations?: AuthorizationUncheckedUpdateManyWithoutDistributorNestedInput
  }

  export type UserCreateManyInput = {
    id?: number
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductCreateInput = {
    name: string
    description?: string | null
    category?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    versions?: ProductVersionCreateNestedManyWithoutProductInput
  }

  export type ProductUncheckedCreateInput = {
    id?: number
    name: string
    description?: string | null
    category?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    versions?: ProductVersionUncheckedCreateNestedManyWithoutProductInput
  }

  export type ProductUpdateInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    versions?: ProductVersionUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    versions?: ProductVersionUncheckedUpdateManyWithoutProductNestedInput
  }

  export type ProductCreateManyInput = {
    id?: number
    name: string
    description?: string | null
    category?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductUpdateManyMutationInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductVersionCreateInput = {
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    product: ProductCreateNestedOneWithoutVersionsInput
    licenses?: LicenseCreateNestedManyWithoutVersionInput
    authorizations?: AuthorizationCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionUncheckedCreateInput = {
    id?: number
    productId: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseUncheckedCreateNestedManyWithoutVersionInput
    authorizations?: AuthorizationUncheckedCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionUpdateInput = {
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    product?: ProductUpdateOneRequiredWithoutVersionsNestedInput
    licenses?: LicenseUpdateManyWithoutVersionNestedInput
    authorizations?: AuthorizationUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    productId?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUncheckedUpdateManyWithoutVersionNestedInput
    authorizations?: AuthorizationUncheckedUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionCreateManyInput = {
    id?: number
    productId: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductVersionUpdateManyMutationInput = {
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductVersionUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    productId?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationCreateInput = {
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    distributor: UserCreateNestedOneWithoutAuthorizationsInput
    version: ProductVersionCreateNestedOneWithoutAuthorizationsInput
  }

  export type AuthorizationUncheckedCreateInput = {
    id?: number
    distributorId: number
    versionId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationUpdateInput = {
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    distributor?: UserUpdateOneRequiredWithoutAuthorizationsNestedInput
    version?: ProductVersionUpdateOneRequiredWithoutAuthorizationsNestedInput
  }

  export type AuthorizationUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    distributorId?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationCreateManyInput = {
    id?: number
    distributorId: number
    versionId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationUpdateManyMutationInput = {
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    distributorId?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseCreateInput = {
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    version: ProductVersionCreateNestedOneWithoutLicensesInput
    distributor: UserCreateNestedOneWithoutLicensesInput
  }

  export type LicenseUncheckedCreateInput = {
    id?: number
    versionId: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    distributorId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseUpdateInput = {
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    version?: ProductVersionUpdateOneRequiredWithoutLicensesNestedInput
    distributor?: UserUpdateOneRequiredWithoutLicensesNestedInput
  }

  export type LicenseUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    distributorId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseCreateManyInput = {
    id?: number
    versionId: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    distributorId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseUpdateManyMutationInput = {
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    distributorId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type EnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type EnumUserStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.UserStatus | EnumUserStatusFieldRefInput<$PrismaModel>
    in?: $Enums.UserStatus[]
    notIn?: $Enums.UserStatus[]
    not?: NestedEnumUserStatusFilter<$PrismaModel> | $Enums.UserStatus
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type LicenseListRelationFilter = {
    every?: LicenseWhereInput
    some?: LicenseWhereInput
    none?: LicenseWhereInput
  }

  export type AuthorizationListRelationFilter = {
    every?: AuthorizationWhereInput
    some?: AuthorizationWhereInput
    none?: AuthorizationWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type LicenseOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AuthorizationOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    passwordHash?: SortOrder
    role?: SortOrder
    status?: SortOrder
    nickName?: SortOrder
    wechat?: SortOrder
    avatar?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    passwordHash?: SortOrder
    role?: SortOrder
    status?: SortOrder
    nickName?: SortOrder
    wechat?: SortOrder
    avatar?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    passwordHash?: SortOrder
    role?: SortOrder
    status?: SortOrder
    nickName?: SortOrder
    wechat?: SortOrder
    avatar?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type EnumUserStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserStatus | EnumUserStatusFieldRefInput<$PrismaModel>
    in?: $Enums.UserStatus[]
    notIn?: $Enums.UserStatus[]
    not?: NestedEnumUserStatusWithAggregatesFilter<$PrismaModel> | $Enums.UserStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserStatusFilter<$PrismaModel>
    _max?: NestedEnumUserStatusFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type EnumProductStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.ProductStatus | EnumProductStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ProductStatus[]
    notIn?: $Enums.ProductStatus[]
    not?: NestedEnumProductStatusFilter<$PrismaModel> | $Enums.ProductStatus
  }

  export type ProductVersionListRelationFilter = {
    every?: ProductVersionWhereInput
    some?: ProductVersionWhereInput
    none?: ProductVersionWhereInput
  }

  export type ProductVersionOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ProductCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    category?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type ProductMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    category?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    category?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type EnumProductStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ProductStatus | EnumProductStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ProductStatus[]
    notIn?: $Enums.ProductStatus[]
    not?: NestedEnumProductStatusWithAggregatesFilter<$PrismaModel> | $Enums.ProductStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumProductStatusFilter<$PrismaModel>
    _max?: NestedEnumProductStatusFilter<$PrismaModel>
  }

  export type FloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type ProductScalarRelationFilter = {
    is?: ProductWhereInput
    isNot?: ProductWhereInput
  }

  export type ProductVersionProductIdVersionCompoundUniqueInput = {
    productId: number
    version: string
  }

  export type ProductVersionCountOrderByAggregateInput = {
    id?: SortOrder
    productId?: SortOrder
    version?: SortOrder
    versionName?: SortOrder
    description?: SortOrder
    verifyTemplate?: SortOrder
    encryptionKey?: SortOrder
    defaultPrice?: SortOrder
    downloadLink?: SortOrder
    coverUrl?: SortOrder
    changelog?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductVersionAvgOrderByAggregateInput = {
    id?: SortOrder
    productId?: SortOrder
    defaultPrice?: SortOrder
  }

  export type ProductVersionMaxOrderByAggregateInput = {
    id?: SortOrder
    productId?: SortOrder
    version?: SortOrder
    versionName?: SortOrder
    description?: SortOrder
    verifyTemplate?: SortOrder
    encryptionKey?: SortOrder
    defaultPrice?: SortOrder
    downloadLink?: SortOrder
    coverUrl?: SortOrder
    changelog?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductVersionMinOrderByAggregateInput = {
    id?: SortOrder
    productId?: SortOrder
    version?: SortOrder
    versionName?: SortOrder
    description?: SortOrder
    verifyTemplate?: SortOrder
    encryptionKey?: SortOrder
    defaultPrice?: SortOrder
    downloadLink?: SortOrder
    coverUrl?: SortOrder
    changelog?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductVersionSumOrderByAggregateInput = {
    id?: SortOrder
    productId?: SortOrder
    defaultPrice?: SortOrder
  }

  export type FloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type EnumAuthorizationStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.AuthorizationStatus | EnumAuthorizationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.AuthorizationStatus[]
    notIn?: $Enums.AuthorizationStatus[]
    not?: NestedEnumAuthorizationStatusFilter<$PrismaModel> | $Enums.AuthorizationStatus
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type ProductVersionScalarRelationFilter = {
    is?: ProductVersionWhereInput
    isNot?: ProductVersionWhereInput
  }

  export type AuthorizationDistributorIdVersionIdCompoundUniqueInput = {
    distributorId: number
    versionId: number
  }

  export type AuthorizationCountOrderByAggregateInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AuthorizationAvgOrderByAggregateInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrder
  }

  export type AuthorizationMaxOrderByAggregateInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AuthorizationMinOrderByAggregateInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AuthorizationSumOrderByAggregateInput = {
    id?: SortOrder
    distributorId?: SortOrder
    versionId?: SortOrder
    customPrice?: SortOrder
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type EnumAuthorizationStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.AuthorizationStatus | EnumAuthorizationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.AuthorizationStatus[]
    notIn?: $Enums.AuthorizationStatus[]
    not?: NestedEnumAuthorizationStatusWithAggregatesFilter<$PrismaModel> | $Enums.AuthorizationStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumAuthorizationStatusFilter<$PrismaModel>
    _max?: NestedEnumAuthorizationStatusFilter<$PrismaModel>
  }

  export type EnumLicenseStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.LicenseStatus | EnumLicenseStatusFieldRefInput<$PrismaModel>
    in?: $Enums.LicenseStatus[]
    notIn?: $Enums.LicenseStatus[]
    not?: NestedEnumLicenseStatusFilter<$PrismaModel> | $Enums.LicenseStatus
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type LicenseCountOrderByAggregateInput = {
    id?: SortOrder
    versionId?: SortOrder
    licenseKey?: SortOrder
    status?: SortOrder
    verifyInstance?: SortOrder
    activatedAt?: SortOrder
    distributorId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type LicenseAvgOrderByAggregateInput = {
    id?: SortOrder
    versionId?: SortOrder
    distributorId?: SortOrder
  }

  export type LicenseMaxOrderByAggregateInput = {
    id?: SortOrder
    versionId?: SortOrder
    licenseKey?: SortOrder
    status?: SortOrder
    verifyInstance?: SortOrder
    activatedAt?: SortOrder
    distributorId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type LicenseMinOrderByAggregateInput = {
    id?: SortOrder
    versionId?: SortOrder
    licenseKey?: SortOrder
    status?: SortOrder
    verifyInstance?: SortOrder
    activatedAt?: SortOrder
    distributorId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type LicenseSumOrderByAggregateInput = {
    id?: SortOrder
    versionId?: SortOrder
    distributorId?: SortOrder
  }

  export type EnumLicenseStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.LicenseStatus | EnumLicenseStatusFieldRefInput<$PrismaModel>
    in?: $Enums.LicenseStatus[]
    notIn?: $Enums.LicenseStatus[]
    not?: NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel> | $Enums.LicenseStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumLicenseStatusFilter<$PrismaModel>
    _max?: NestedEnumLicenseStatusFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type LicenseCreateNestedManyWithoutDistributorInput = {
    create?: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput> | LicenseCreateWithoutDistributorInput[] | LicenseUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutDistributorInput | LicenseCreateOrConnectWithoutDistributorInput[]
    createMany?: LicenseCreateManyDistributorInputEnvelope
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
  }

  export type AuthorizationCreateNestedManyWithoutDistributorInput = {
    create?: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput> | AuthorizationCreateWithoutDistributorInput[] | AuthorizationUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutDistributorInput | AuthorizationCreateOrConnectWithoutDistributorInput[]
    createMany?: AuthorizationCreateManyDistributorInputEnvelope
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
  }

  export type LicenseUncheckedCreateNestedManyWithoutDistributorInput = {
    create?: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput> | LicenseCreateWithoutDistributorInput[] | LicenseUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutDistributorInput | LicenseCreateOrConnectWithoutDistributorInput[]
    createMany?: LicenseCreateManyDistributorInputEnvelope
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
  }

  export type AuthorizationUncheckedCreateNestedManyWithoutDistributorInput = {
    create?: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput> | AuthorizationCreateWithoutDistributorInput[] | AuthorizationUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutDistributorInput | AuthorizationCreateOrConnectWithoutDistributorInput[]
    createMany?: AuthorizationCreateManyDistributorInputEnvelope
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type EnumUserRoleFieldUpdateOperationsInput = {
    set?: $Enums.UserRole
  }

  export type EnumUserStatusFieldUpdateOperationsInput = {
    set?: $Enums.UserStatus
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type LicenseUpdateManyWithoutDistributorNestedInput = {
    create?: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput> | LicenseCreateWithoutDistributorInput[] | LicenseUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutDistributorInput | LicenseCreateOrConnectWithoutDistributorInput[]
    upsert?: LicenseUpsertWithWhereUniqueWithoutDistributorInput | LicenseUpsertWithWhereUniqueWithoutDistributorInput[]
    createMany?: LicenseCreateManyDistributorInputEnvelope
    set?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    disconnect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    delete?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    update?: LicenseUpdateWithWhereUniqueWithoutDistributorInput | LicenseUpdateWithWhereUniqueWithoutDistributorInput[]
    updateMany?: LicenseUpdateManyWithWhereWithoutDistributorInput | LicenseUpdateManyWithWhereWithoutDistributorInput[]
    deleteMany?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
  }

  export type AuthorizationUpdateManyWithoutDistributorNestedInput = {
    create?: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput> | AuthorizationCreateWithoutDistributorInput[] | AuthorizationUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutDistributorInput | AuthorizationCreateOrConnectWithoutDistributorInput[]
    upsert?: AuthorizationUpsertWithWhereUniqueWithoutDistributorInput | AuthorizationUpsertWithWhereUniqueWithoutDistributorInput[]
    createMany?: AuthorizationCreateManyDistributorInputEnvelope
    set?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    disconnect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    delete?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    update?: AuthorizationUpdateWithWhereUniqueWithoutDistributorInput | AuthorizationUpdateWithWhereUniqueWithoutDistributorInput[]
    updateMany?: AuthorizationUpdateManyWithWhereWithoutDistributorInput | AuthorizationUpdateManyWithWhereWithoutDistributorInput[]
    deleteMany?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type LicenseUncheckedUpdateManyWithoutDistributorNestedInput = {
    create?: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput> | LicenseCreateWithoutDistributorInput[] | LicenseUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutDistributorInput | LicenseCreateOrConnectWithoutDistributorInput[]
    upsert?: LicenseUpsertWithWhereUniqueWithoutDistributorInput | LicenseUpsertWithWhereUniqueWithoutDistributorInput[]
    createMany?: LicenseCreateManyDistributorInputEnvelope
    set?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    disconnect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    delete?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    update?: LicenseUpdateWithWhereUniqueWithoutDistributorInput | LicenseUpdateWithWhereUniqueWithoutDistributorInput[]
    updateMany?: LicenseUpdateManyWithWhereWithoutDistributorInput | LicenseUpdateManyWithWhereWithoutDistributorInput[]
    deleteMany?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
  }

  export type AuthorizationUncheckedUpdateManyWithoutDistributorNestedInput = {
    create?: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput> | AuthorizationCreateWithoutDistributorInput[] | AuthorizationUncheckedCreateWithoutDistributorInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutDistributorInput | AuthorizationCreateOrConnectWithoutDistributorInput[]
    upsert?: AuthorizationUpsertWithWhereUniqueWithoutDistributorInput | AuthorizationUpsertWithWhereUniqueWithoutDistributorInput[]
    createMany?: AuthorizationCreateManyDistributorInputEnvelope
    set?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    disconnect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    delete?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    update?: AuthorizationUpdateWithWhereUniqueWithoutDistributorInput | AuthorizationUpdateWithWhereUniqueWithoutDistributorInput[]
    updateMany?: AuthorizationUpdateManyWithWhereWithoutDistributorInput | AuthorizationUpdateManyWithWhereWithoutDistributorInput[]
    deleteMany?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
  }

  export type ProductVersionCreateNestedManyWithoutProductInput = {
    create?: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput> | ProductVersionCreateWithoutProductInput[] | ProductVersionUncheckedCreateWithoutProductInput[]
    connectOrCreate?: ProductVersionCreateOrConnectWithoutProductInput | ProductVersionCreateOrConnectWithoutProductInput[]
    createMany?: ProductVersionCreateManyProductInputEnvelope
    connect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
  }

  export type ProductVersionUncheckedCreateNestedManyWithoutProductInput = {
    create?: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput> | ProductVersionCreateWithoutProductInput[] | ProductVersionUncheckedCreateWithoutProductInput[]
    connectOrCreate?: ProductVersionCreateOrConnectWithoutProductInput | ProductVersionCreateOrConnectWithoutProductInput[]
    createMany?: ProductVersionCreateManyProductInputEnvelope
    connect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
  }

  export type EnumProductStatusFieldUpdateOperationsInput = {
    set?: $Enums.ProductStatus
  }

  export type ProductVersionUpdateManyWithoutProductNestedInput = {
    create?: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput> | ProductVersionCreateWithoutProductInput[] | ProductVersionUncheckedCreateWithoutProductInput[]
    connectOrCreate?: ProductVersionCreateOrConnectWithoutProductInput | ProductVersionCreateOrConnectWithoutProductInput[]
    upsert?: ProductVersionUpsertWithWhereUniqueWithoutProductInput | ProductVersionUpsertWithWhereUniqueWithoutProductInput[]
    createMany?: ProductVersionCreateManyProductInputEnvelope
    set?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    disconnect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    delete?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    connect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    update?: ProductVersionUpdateWithWhereUniqueWithoutProductInput | ProductVersionUpdateWithWhereUniqueWithoutProductInput[]
    updateMany?: ProductVersionUpdateManyWithWhereWithoutProductInput | ProductVersionUpdateManyWithWhereWithoutProductInput[]
    deleteMany?: ProductVersionScalarWhereInput | ProductVersionScalarWhereInput[]
  }

  export type ProductVersionUncheckedUpdateManyWithoutProductNestedInput = {
    create?: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput> | ProductVersionCreateWithoutProductInput[] | ProductVersionUncheckedCreateWithoutProductInput[]
    connectOrCreate?: ProductVersionCreateOrConnectWithoutProductInput | ProductVersionCreateOrConnectWithoutProductInput[]
    upsert?: ProductVersionUpsertWithWhereUniqueWithoutProductInput | ProductVersionUpsertWithWhereUniqueWithoutProductInput[]
    createMany?: ProductVersionCreateManyProductInputEnvelope
    set?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    disconnect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    delete?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    connect?: ProductVersionWhereUniqueInput | ProductVersionWhereUniqueInput[]
    update?: ProductVersionUpdateWithWhereUniqueWithoutProductInput | ProductVersionUpdateWithWhereUniqueWithoutProductInput[]
    updateMany?: ProductVersionUpdateManyWithWhereWithoutProductInput | ProductVersionUpdateManyWithWhereWithoutProductInput[]
    deleteMany?: ProductVersionScalarWhereInput | ProductVersionScalarWhereInput[]
  }

  export type ProductCreateNestedOneWithoutVersionsInput = {
    create?: XOR<ProductCreateWithoutVersionsInput, ProductUncheckedCreateWithoutVersionsInput>
    connectOrCreate?: ProductCreateOrConnectWithoutVersionsInput
    connect?: ProductWhereUniqueInput
  }

  export type LicenseCreateNestedManyWithoutVersionInput = {
    create?: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput> | LicenseCreateWithoutVersionInput[] | LicenseUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutVersionInput | LicenseCreateOrConnectWithoutVersionInput[]
    createMany?: LicenseCreateManyVersionInputEnvelope
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
  }

  export type AuthorizationCreateNestedManyWithoutVersionInput = {
    create?: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput> | AuthorizationCreateWithoutVersionInput[] | AuthorizationUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutVersionInput | AuthorizationCreateOrConnectWithoutVersionInput[]
    createMany?: AuthorizationCreateManyVersionInputEnvelope
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
  }

  export type LicenseUncheckedCreateNestedManyWithoutVersionInput = {
    create?: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput> | LicenseCreateWithoutVersionInput[] | LicenseUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutVersionInput | LicenseCreateOrConnectWithoutVersionInput[]
    createMany?: LicenseCreateManyVersionInputEnvelope
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
  }

  export type AuthorizationUncheckedCreateNestedManyWithoutVersionInput = {
    create?: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput> | AuthorizationCreateWithoutVersionInput[] | AuthorizationUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutVersionInput | AuthorizationCreateOrConnectWithoutVersionInput[]
    createMany?: AuthorizationCreateManyVersionInputEnvelope
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
  }

  export type FloatFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type ProductUpdateOneRequiredWithoutVersionsNestedInput = {
    create?: XOR<ProductCreateWithoutVersionsInput, ProductUncheckedCreateWithoutVersionsInput>
    connectOrCreate?: ProductCreateOrConnectWithoutVersionsInput
    upsert?: ProductUpsertWithoutVersionsInput
    connect?: ProductWhereUniqueInput
    update?: XOR<XOR<ProductUpdateToOneWithWhereWithoutVersionsInput, ProductUpdateWithoutVersionsInput>, ProductUncheckedUpdateWithoutVersionsInput>
  }

  export type LicenseUpdateManyWithoutVersionNestedInput = {
    create?: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput> | LicenseCreateWithoutVersionInput[] | LicenseUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutVersionInput | LicenseCreateOrConnectWithoutVersionInput[]
    upsert?: LicenseUpsertWithWhereUniqueWithoutVersionInput | LicenseUpsertWithWhereUniqueWithoutVersionInput[]
    createMany?: LicenseCreateManyVersionInputEnvelope
    set?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    disconnect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    delete?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    update?: LicenseUpdateWithWhereUniqueWithoutVersionInput | LicenseUpdateWithWhereUniqueWithoutVersionInput[]
    updateMany?: LicenseUpdateManyWithWhereWithoutVersionInput | LicenseUpdateManyWithWhereWithoutVersionInput[]
    deleteMany?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
  }

  export type AuthorizationUpdateManyWithoutVersionNestedInput = {
    create?: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput> | AuthorizationCreateWithoutVersionInput[] | AuthorizationUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutVersionInput | AuthorizationCreateOrConnectWithoutVersionInput[]
    upsert?: AuthorizationUpsertWithWhereUniqueWithoutVersionInput | AuthorizationUpsertWithWhereUniqueWithoutVersionInput[]
    createMany?: AuthorizationCreateManyVersionInputEnvelope
    set?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    disconnect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    delete?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    update?: AuthorizationUpdateWithWhereUniqueWithoutVersionInput | AuthorizationUpdateWithWhereUniqueWithoutVersionInput[]
    updateMany?: AuthorizationUpdateManyWithWhereWithoutVersionInput | AuthorizationUpdateManyWithWhereWithoutVersionInput[]
    deleteMany?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
  }

  export type LicenseUncheckedUpdateManyWithoutVersionNestedInput = {
    create?: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput> | LicenseCreateWithoutVersionInput[] | LicenseUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: LicenseCreateOrConnectWithoutVersionInput | LicenseCreateOrConnectWithoutVersionInput[]
    upsert?: LicenseUpsertWithWhereUniqueWithoutVersionInput | LicenseUpsertWithWhereUniqueWithoutVersionInput[]
    createMany?: LicenseCreateManyVersionInputEnvelope
    set?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    disconnect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    delete?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    connect?: LicenseWhereUniqueInput | LicenseWhereUniqueInput[]
    update?: LicenseUpdateWithWhereUniqueWithoutVersionInput | LicenseUpdateWithWhereUniqueWithoutVersionInput[]
    updateMany?: LicenseUpdateManyWithWhereWithoutVersionInput | LicenseUpdateManyWithWhereWithoutVersionInput[]
    deleteMany?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
  }

  export type AuthorizationUncheckedUpdateManyWithoutVersionNestedInput = {
    create?: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput> | AuthorizationCreateWithoutVersionInput[] | AuthorizationUncheckedCreateWithoutVersionInput[]
    connectOrCreate?: AuthorizationCreateOrConnectWithoutVersionInput | AuthorizationCreateOrConnectWithoutVersionInput[]
    upsert?: AuthorizationUpsertWithWhereUniqueWithoutVersionInput | AuthorizationUpsertWithWhereUniqueWithoutVersionInput[]
    createMany?: AuthorizationCreateManyVersionInputEnvelope
    set?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    disconnect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    delete?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    connect?: AuthorizationWhereUniqueInput | AuthorizationWhereUniqueInput[]
    update?: AuthorizationUpdateWithWhereUniqueWithoutVersionInput | AuthorizationUpdateWithWhereUniqueWithoutVersionInput[]
    updateMany?: AuthorizationUpdateManyWithWhereWithoutVersionInput | AuthorizationUpdateManyWithWhereWithoutVersionInput[]
    deleteMany?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutAuthorizationsInput = {
    create?: XOR<UserCreateWithoutAuthorizationsInput, UserUncheckedCreateWithoutAuthorizationsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAuthorizationsInput
    connect?: UserWhereUniqueInput
  }

  export type ProductVersionCreateNestedOneWithoutAuthorizationsInput = {
    create?: XOR<ProductVersionCreateWithoutAuthorizationsInput, ProductVersionUncheckedCreateWithoutAuthorizationsInput>
    connectOrCreate?: ProductVersionCreateOrConnectWithoutAuthorizationsInput
    connect?: ProductVersionWhereUniqueInput
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type EnumAuthorizationStatusFieldUpdateOperationsInput = {
    set?: $Enums.AuthorizationStatus
  }

  export type UserUpdateOneRequiredWithoutAuthorizationsNestedInput = {
    create?: XOR<UserCreateWithoutAuthorizationsInput, UserUncheckedCreateWithoutAuthorizationsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAuthorizationsInput
    upsert?: UserUpsertWithoutAuthorizationsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutAuthorizationsInput, UserUpdateWithoutAuthorizationsInput>, UserUncheckedUpdateWithoutAuthorizationsInput>
  }

  export type ProductVersionUpdateOneRequiredWithoutAuthorizationsNestedInput = {
    create?: XOR<ProductVersionCreateWithoutAuthorizationsInput, ProductVersionUncheckedCreateWithoutAuthorizationsInput>
    connectOrCreate?: ProductVersionCreateOrConnectWithoutAuthorizationsInput
    upsert?: ProductVersionUpsertWithoutAuthorizationsInput
    connect?: ProductVersionWhereUniqueInput
    update?: XOR<XOR<ProductVersionUpdateToOneWithWhereWithoutAuthorizationsInput, ProductVersionUpdateWithoutAuthorizationsInput>, ProductVersionUncheckedUpdateWithoutAuthorizationsInput>
  }

  export type ProductVersionCreateNestedOneWithoutLicensesInput = {
    create?: XOR<ProductVersionCreateWithoutLicensesInput, ProductVersionUncheckedCreateWithoutLicensesInput>
    connectOrCreate?: ProductVersionCreateOrConnectWithoutLicensesInput
    connect?: ProductVersionWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutLicensesInput = {
    create?: XOR<UserCreateWithoutLicensesInput, UserUncheckedCreateWithoutLicensesInput>
    connectOrCreate?: UserCreateOrConnectWithoutLicensesInput
    connect?: UserWhereUniqueInput
  }

  export type EnumLicenseStatusFieldUpdateOperationsInput = {
    set?: $Enums.LicenseStatus
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type ProductVersionUpdateOneRequiredWithoutLicensesNestedInput = {
    create?: XOR<ProductVersionCreateWithoutLicensesInput, ProductVersionUncheckedCreateWithoutLicensesInput>
    connectOrCreate?: ProductVersionCreateOrConnectWithoutLicensesInput
    upsert?: ProductVersionUpsertWithoutLicensesInput
    connect?: ProductVersionWhereUniqueInput
    update?: XOR<XOR<ProductVersionUpdateToOneWithWhereWithoutLicensesInput, ProductVersionUpdateWithoutLicensesInput>, ProductVersionUncheckedUpdateWithoutLicensesInput>
  }

  export type UserUpdateOneRequiredWithoutLicensesNestedInput = {
    create?: XOR<UserCreateWithoutLicensesInput, UserUncheckedCreateWithoutLicensesInput>
    connectOrCreate?: UserCreateOrConnectWithoutLicensesInput
    upsert?: UserUpsertWithoutLicensesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutLicensesInput, UserUpdateWithoutLicensesInput>, UserUncheckedUpdateWithoutLicensesInput>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type NestedEnumUserStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.UserStatus | EnumUserStatusFieldRefInput<$PrismaModel>
    in?: $Enums.UserStatus[]
    notIn?: $Enums.UserStatus[]
    not?: NestedEnumUserStatusFilter<$PrismaModel> | $Enums.UserStatus
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type NestedEnumUserStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserStatus | EnumUserStatusFieldRefInput<$PrismaModel>
    in?: $Enums.UserStatus[]
    notIn?: $Enums.UserStatus[]
    not?: NestedEnumUserStatusWithAggregatesFilter<$PrismaModel> | $Enums.UserStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserStatusFilter<$PrismaModel>
    _max?: NestedEnumUserStatusFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedEnumProductStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.ProductStatus | EnumProductStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ProductStatus[]
    notIn?: $Enums.ProductStatus[]
    not?: NestedEnumProductStatusFilter<$PrismaModel> | $Enums.ProductStatus
  }

  export type NestedEnumProductStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ProductStatus | EnumProductStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ProductStatus[]
    notIn?: $Enums.ProductStatus[]
    not?: NestedEnumProductStatusWithAggregatesFilter<$PrismaModel> | $Enums.ProductStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumProductStatusFilter<$PrismaModel>
    _max?: NestedEnumProductStatusFilter<$PrismaModel>
  }

  export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedEnumAuthorizationStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.AuthorizationStatus | EnumAuthorizationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.AuthorizationStatus[]
    notIn?: $Enums.AuthorizationStatus[]
    not?: NestedEnumAuthorizationStatusFilter<$PrismaModel> | $Enums.AuthorizationStatus
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type NestedEnumAuthorizationStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.AuthorizationStatus | EnumAuthorizationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.AuthorizationStatus[]
    notIn?: $Enums.AuthorizationStatus[]
    not?: NestedEnumAuthorizationStatusWithAggregatesFilter<$PrismaModel> | $Enums.AuthorizationStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumAuthorizationStatusFilter<$PrismaModel>
    _max?: NestedEnumAuthorizationStatusFilter<$PrismaModel>
  }

  export type NestedEnumLicenseStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.LicenseStatus | EnumLicenseStatusFieldRefInput<$PrismaModel>
    in?: $Enums.LicenseStatus[]
    notIn?: $Enums.LicenseStatus[]
    not?: NestedEnumLicenseStatusFilter<$PrismaModel> | $Enums.LicenseStatus
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.LicenseStatus | EnumLicenseStatusFieldRefInput<$PrismaModel>
    in?: $Enums.LicenseStatus[]
    notIn?: $Enums.LicenseStatus[]
    not?: NestedEnumLicenseStatusWithAggregatesFilter<$PrismaModel> | $Enums.LicenseStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumLicenseStatusFilter<$PrismaModel>
    _max?: NestedEnumLicenseStatusFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type LicenseCreateWithoutDistributorInput = {
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    version: ProductVersionCreateNestedOneWithoutLicensesInput
  }

  export type LicenseUncheckedCreateWithoutDistributorInput = {
    id?: number
    versionId: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseCreateOrConnectWithoutDistributorInput = {
    where: LicenseWhereUniqueInput
    create: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput>
  }

  export type LicenseCreateManyDistributorInputEnvelope = {
    data: LicenseCreateManyDistributorInput | LicenseCreateManyDistributorInput[]
  }

  export type AuthorizationCreateWithoutDistributorInput = {
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    version: ProductVersionCreateNestedOneWithoutAuthorizationsInput
  }

  export type AuthorizationUncheckedCreateWithoutDistributorInput = {
    id?: number
    versionId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationCreateOrConnectWithoutDistributorInput = {
    where: AuthorizationWhereUniqueInput
    create: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput>
  }

  export type AuthorizationCreateManyDistributorInputEnvelope = {
    data: AuthorizationCreateManyDistributorInput | AuthorizationCreateManyDistributorInput[]
  }

  export type LicenseUpsertWithWhereUniqueWithoutDistributorInput = {
    where: LicenseWhereUniqueInput
    update: XOR<LicenseUpdateWithoutDistributorInput, LicenseUncheckedUpdateWithoutDistributorInput>
    create: XOR<LicenseCreateWithoutDistributorInput, LicenseUncheckedCreateWithoutDistributorInput>
  }

  export type LicenseUpdateWithWhereUniqueWithoutDistributorInput = {
    where: LicenseWhereUniqueInput
    data: XOR<LicenseUpdateWithoutDistributorInput, LicenseUncheckedUpdateWithoutDistributorInput>
  }

  export type LicenseUpdateManyWithWhereWithoutDistributorInput = {
    where: LicenseScalarWhereInput
    data: XOR<LicenseUpdateManyMutationInput, LicenseUncheckedUpdateManyWithoutDistributorInput>
  }

  export type LicenseScalarWhereInput = {
    AND?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
    OR?: LicenseScalarWhereInput[]
    NOT?: LicenseScalarWhereInput | LicenseScalarWhereInput[]
    id?: IntFilter<"License"> | number
    versionId?: IntFilter<"License"> | number
    licenseKey?: StringFilter<"License"> | string
    status?: EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
    verifyInstance?: StringNullableFilter<"License"> | string | null
    activatedAt?: DateTimeNullableFilter<"License"> | Date | string | null
    distributorId?: IntFilter<"License"> | number
    createdAt?: DateTimeFilter<"License"> | Date | string
    updatedAt?: DateTimeFilter<"License"> | Date | string
  }

  export type AuthorizationUpsertWithWhereUniqueWithoutDistributorInput = {
    where: AuthorizationWhereUniqueInput
    update: XOR<AuthorizationUpdateWithoutDistributorInput, AuthorizationUncheckedUpdateWithoutDistributorInput>
    create: XOR<AuthorizationCreateWithoutDistributorInput, AuthorizationUncheckedCreateWithoutDistributorInput>
  }

  export type AuthorizationUpdateWithWhereUniqueWithoutDistributorInput = {
    where: AuthorizationWhereUniqueInput
    data: XOR<AuthorizationUpdateWithoutDistributorInput, AuthorizationUncheckedUpdateWithoutDistributorInput>
  }

  export type AuthorizationUpdateManyWithWhereWithoutDistributorInput = {
    where: AuthorizationScalarWhereInput
    data: XOR<AuthorizationUpdateManyMutationInput, AuthorizationUncheckedUpdateManyWithoutDistributorInput>
  }

  export type AuthorizationScalarWhereInput = {
    AND?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
    OR?: AuthorizationScalarWhereInput[]
    NOT?: AuthorizationScalarWhereInput | AuthorizationScalarWhereInput[]
    id?: IntFilter<"Authorization"> | number
    distributorId?: IntFilter<"Authorization"> | number
    versionId?: IntFilter<"Authorization"> | number
    customPrice?: FloatNullableFilter<"Authorization"> | number | null
    status?: EnumAuthorizationStatusFilter<"Authorization"> | $Enums.AuthorizationStatus
    createdAt?: DateTimeFilter<"Authorization"> | Date | string
    updatedAt?: DateTimeFilter<"Authorization"> | Date | string
  }

  export type ProductVersionCreateWithoutProductInput = {
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseCreateNestedManyWithoutVersionInput
    authorizations?: AuthorizationCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionUncheckedCreateWithoutProductInput = {
    id?: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseUncheckedCreateNestedManyWithoutVersionInput
    authorizations?: AuthorizationUncheckedCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionCreateOrConnectWithoutProductInput = {
    where: ProductVersionWhereUniqueInput
    create: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput>
  }

  export type ProductVersionCreateManyProductInputEnvelope = {
    data: ProductVersionCreateManyProductInput | ProductVersionCreateManyProductInput[]
  }

  export type ProductVersionUpsertWithWhereUniqueWithoutProductInput = {
    where: ProductVersionWhereUniqueInput
    update: XOR<ProductVersionUpdateWithoutProductInput, ProductVersionUncheckedUpdateWithoutProductInput>
    create: XOR<ProductVersionCreateWithoutProductInput, ProductVersionUncheckedCreateWithoutProductInput>
  }

  export type ProductVersionUpdateWithWhereUniqueWithoutProductInput = {
    where: ProductVersionWhereUniqueInput
    data: XOR<ProductVersionUpdateWithoutProductInput, ProductVersionUncheckedUpdateWithoutProductInput>
  }

  export type ProductVersionUpdateManyWithWhereWithoutProductInput = {
    where: ProductVersionScalarWhereInput
    data: XOR<ProductVersionUpdateManyMutationInput, ProductVersionUncheckedUpdateManyWithoutProductInput>
  }

  export type ProductVersionScalarWhereInput = {
    AND?: ProductVersionScalarWhereInput | ProductVersionScalarWhereInput[]
    OR?: ProductVersionScalarWhereInput[]
    NOT?: ProductVersionScalarWhereInput | ProductVersionScalarWhereInput[]
    id?: IntFilter<"ProductVersion"> | number
    productId?: IntFilter<"ProductVersion"> | number
    version?: StringFilter<"ProductVersion"> | string
    versionName?: StringNullableFilter<"ProductVersion"> | string | null
    description?: StringNullableFilter<"ProductVersion"> | string | null
    verifyTemplate?: StringFilter<"ProductVersion"> | string
    encryptionKey?: StringFilter<"ProductVersion"> | string
    defaultPrice?: FloatFilter<"ProductVersion"> | number
    downloadLink?: StringNullableFilter<"ProductVersion"> | string | null
    coverUrl?: StringNullableFilter<"ProductVersion"> | string | null
    changelog?: StringNullableFilter<"ProductVersion"> | string | null
    status?: EnumProductStatusFilter<"ProductVersion"> | $Enums.ProductStatus
    createdAt?: DateTimeFilter<"ProductVersion"> | Date | string
    updatedAt?: DateTimeFilter<"ProductVersion"> | Date | string
  }

  export type ProductCreateWithoutVersionsInput = {
    name: string
    description?: string | null
    category?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductUncheckedCreateWithoutVersionsInput = {
    id?: number
    name: string
    description?: string | null
    category?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductCreateOrConnectWithoutVersionsInput = {
    where: ProductWhereUniqueInput
    create: XOR<ProductCreateWithoutVersionsInput, ProductUncheckedCreateWithoutVersionsInput>
  }

  export type LicenseCreateWithoutVersionInput = {
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    distributor: UserCreateNestedOneWithoutLicensesInput
  }

  export type LicenseUncheckedCreateWithoutVersionInput = {
    id?: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    distributorId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseCreateOrConnectWithoutVersionInput = {
    where: LicenseWhereUniqueInput
    create: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput>
  }

  export type LicenseCreateManyVersionInputEnvelope = {
    data: LicenseCreateManyVersionInput | LicenseCreateManyVersionInput[]
  }

  export type AuthorizationCreateWithoutVersionInput = {
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    distributor: UserCreateNestedOneWithoutAuthorizationsInput
  }

  export type AuthorizationUncheckedCreateWithoutVersionInput = {
    id?: number
    distributorId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationCreateOrConnectWithoutVersionInput = {
    where: AuthorizationWhereUniqueInput
    create: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput>
  }

  export type AuthorizationCreateManyVersionInputEnvelope = {
    data: AuthorizationCreateManyVersionInput | AuthorizationCreateManyVersionInput[]
  }

  export type ProductUpsertWithoutVersionsInput = {
    update: XOR<ProductUpdateWithoutVersionsInput, ProductUncheckedUpdateWithoutVersionsInput>
    create: XOR<ProductCreateWithoutVersionsInput, ProductUncheckedCreateWithoutVersionsInput>
    where?: ProductWhereInput
  }

  export type ProductUpdateToOneWithWhereWithoutVersionsInput = {
    where?: ProductWhereInput
    data: XOR<ProductUpdateWithoutVersionsInput, ProductUncheckedUpdateWithoutVersionsInput>
  }

  export type ProductUpdateWithoutVersionsInput = {
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductUncheckedUpdateWithoutVersionsInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseUpsertWithWhereUniqueWithoutVersionInput = {
    where: LicenseWhereUniqueInput
    update: XOR<LicenseUpdateWithoutVersionInput, LicenseUncheckedUpdateWithoutVersionInput>
    create: XOR<LicenseCreateWithoutVersionInput, LicenseUncheckedCreateWithoutVersionInput>
  }

  export type LicenseUpdateWithWhereUniqueWithoutVersionInput = {
    where: LicenseWhereUniqueInput
    data: XOR<LicenseUpdateWithoutVersionInput, LicenseUncheckedUpdateWithoutVersionInput>
  }

  export type LicenseUpdateManyWithWhereWithoutVersionInput = {
    where: LicenseScalarWhereInput
    data: XOR<LicenseUpdateManyMutationInput, LicenseUncheckedUpdateManyWithoutVersionInput>
  }

  export type AuthorizationUpsertWithWhereUniqueWithoutVersionInput = {
    where: AuthorizationWhereUniqueInput
    update: XOR<AuthorizationUpdateWithoutVersionInput, AuthorizationUncheckedUpdateWithoutVersionInput>
    create: XOR<AuthorizationCreateWithoutVersionInput, AuthorizationUncheckedCreateWithoutVersionInput>
  }

  export type AuthorizationUpdateWithWhereUniqueWithoutVersionInput = {
    where: AuthorizationWhereUniqueInput
    data: XOR<AuthorizationUpdateWithoutVersionInput, AuthorizationUncheckedUpdateWithoutVersionInput>
  }

  export type AuthorizationUpdateManyWithWhereWithoutVersionInput = {
    where: AuthorizationScalarWhereInput
    data: XOR<AuthorizationUpdateManyMutationInput, AuthorizationUncheckedUpdateManyWithoutVersionInput>
  }

  export type UserCreateWithoutAuthorizationsInput = {
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseCreateNestedManyWithoutDistributorInput
  }

  export type UserUncheckedCreateWithoutAuthorizationsInput = {
    id?: number
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseUncheckedCreateNestedManyWithoutDistributorInput
  }

  export type UserCreateOrConnectWithoutAuthorizationsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutAuthorizationsInput, UserUncheckedCreateWithoutAuthorizationsInput>
  }

  export type ProductVersionCreateWithoutAuthorizationsInput = {
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    product: ProductCreateNestedOneWithoutVersionsInput
    licenses?: LicenseCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionUncheckedCreateWithoutAuthorizationsInput = {
    id?: number
    productId: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    licenses?: LicenseUncheckedCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionCreateOrConnectWithoutAuthorizationsInput = {
    where: ProductVersionWhereUniqueInput
    create: XOR<ProductVersionCreateWithoutAuthorizationsInput, ProductVersionUncheckedCreateWithoutAuthorizationsInput>
  }

  export type UserUpsertWithoutAuthorizationsInput = {
    update: XOR<UserUpdateWithoutAuthorizationsInput, UserUncheckedUpdateWithoutAuthorizationsInput>
    create: XOR<UserCreateWithoutAuthorizationsInput, UserUncheckedCreateWithoutAuthorizationsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutAuthorizationsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutAuthorizationsInput, UserUncheckedUpdateWithoutAuthorizationsInput>
  }

  export type UserUpdateWithoutAuthorizationsInput = {
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUpdateManyWithoutDistributorNestedInput
  }

  export type UserUncheckedUpdateWithoutAuthorizationsInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUncheckedUpdateManyWithoutDistributorNestedInput
  }

  export type ProductVersionUpsertWithoutAuthorizationsInput = {
    update: XOR<ProductVersionUpdateWithoutAuthorizationsInput, ProductVersionUncheckedUpdateWithoutAuthorizationsInput>
    create: XOR<ProductVersionCreateWithoutAuthorizationsInput, ProductVersionUncheckedCreateWithoutAuthorizationsInput>
    where?: ProductVersionWhereInput
  }

  export type ProductVersionUpdateToOneWithWhereWithoutAuthorizationsInput = {
    where?: ProductVersionWhereInput
    data: XOR<ProductVersionUpdateWithoutAuthorizationsInput, ProductVersionUncheckedUpdateWithoutAuthorizationsInput>
  }

  export type ProductVersionUpdateWithoutAuthorizationsInput = {
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    product?: ProductUpdateOneRequiredWithoutVersionsNestedInput
    licenses?: LicenseUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionUncheckedUpdateWithoutAuthorizationsInput = {
    id?: IntFieldUpdateOperationsInput | number
    productId?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUncheckedUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionCreateWithoutLicensesInput = {
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    product: ProductCreateNestedOneWithoutVersionsInput
    authorizations?: AuthorizationCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionUncheckedCreateWithoutLicensesInput = {
    id?: number
    productId: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    authorizations?: AuthorizationUncheckedCreateNestedManyWithoutVersionInput
  }

  export type ProductVersionCreateOrConnectWithoutLicensesInput = {
    where: ProductVersionWhereUniqueInput
    create: XOR<ProductVersionCreateWithoutLicensesInput, ProductVersionUncheckedCreateWithoutLicensesInput>
  }

  export type UserCreateWithoutLicensesInput = {
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorizations?: AuthorizationCreateNestedManyWithoutDistributorInput
  }

  export type UserUncheckedCreateWithoutLicensesInput = {
    id?: number
    username: string
    passwordHash: string
    role: $Enums.UserRole
    status?: $Enums.UserStatus
    nickName?: string | null
    wechat?: string | null
    avatar?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorizations?: AuthorizationUncheckedCreateNestedManyWithoutDistributorInput
  }

  export type UserCreateOrConnectWithoutLicensesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutLicensesInput, UserUncheckedCreateWithoutLicensesInput>
  }

  export type ProductVersionUpsertWithoutLicensesInput = {
    update: XOR<ProductVersionUpdateWithoutLicensesInput, ProductVersionUncheckedUpdateWithoutLicensesInput>
    create: XOR<ProductVersionCreateWithoutLicensesInput, ProductVersionUncheckedCreateWithoutLicensesInput>
    where?: ProductVersionWhereInput
  }

  export type ProductVersionUpdateToOneWithWhereWithoutLicensesInput = {
    where?: ProductVersionWhereInput
    data: XOR<ProductVersionUpdateWithoutLicensesInput, ProductVersionUncheckedUpdateWithoutLicensesInput>
  }

  export type ProductVersionUpdateWithoutLicensesInput = {
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    product?: ProductUpdateOneRequiredWithoutVersionsNestedInput
    authorizations?: AuthorizationUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionUncheckedUpdateWithoutLicensesInput = {
    id?: IntFieldUpdateOperationsInput | number
    productId?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorizations?: AuthorizationUncheckedUpdateManyWithoutVersionNestedInput
  }

  export type UserUpsertWithoutLicensesInput = {
    update: XOR<UserUpdateWithoutLicensesInput, UserUncheckedUpdateWithoutLicensesInput>
    create: XOR<UserCreateWithoutLicensesInput, UserUncheckedCreateWithoutLicensesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutLicensesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutLicensesInput, UserUncheckedUpdateWithoutLicensesInput>
  }

  export type UserUpdateWithoutLicensesInput = {
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorizations?: AuthorizationUpdateManyWithoutDistributorNestedInput
  }

  export type UserUncheckedUpdateWithoutLicensesInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    status?: EnumUserStatusFieldUpdateOperationsInput | $Enums.UserStatus
    nickName?: NullableStringFieldUpdateOperationsInput | string | null
    wechat?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorizations?: AuthorizationUncheckedUpdateManyWithoutDistributorNestedInput
  }

  export type LicenseCreateManyDistributorInput = {
    id?: number
    versionId: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationCreateManyDistributorInput = {
    id?: number
    versionId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseUpdateWithoutDistributorInput = {
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    version?: ProductVersionUpdateOneRequiredWithoutLicensesNestedInput
  }

  export type LicenseUncheckedUpdateWithoutDistributorInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseUncheckedUpdateManyWithoutDistributorInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationUpdateWithoutDistributorInput = {
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    version?: ProductVersionUpdateOneRequiredWithoutAuthorizationsNestedInput
  }

  export type AuthorizationUncheckedUpdateWithoutDistributorInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationUncheckedUpdateManyWithoutDistributorInput = {
    id?: IntFieldUpdateOperationsInput | number
    versionId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductVersionCreateManyProductInput = {
    id?: number
    version: string
    versionName?: string | null
    description?: string | null
    verifyTemplate: string
    encryptionKey: string
    defaultPrice: number
    downloadLink?: string | null
    coverUrl?: string | null
    changelog?: string | null
    status?: $Enums.ProductStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductVersionUpdateWithoutProductInput = {
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUpdateManyWithoutVersionNestedInput
    authorizations?: AuthorizationUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionUncheckedUpdateWithoutProductInput = {
    id?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    licenses?: LicenseUncheckedUpdateManyWithoutVersionNestedInput
    authorizations?: AuthorizationUncheckedUpdateManyWithoutVersionNestedInput
  }

  export type ProductVersionUncheckedUpdateManyWithoutProductInput = {
    id?: IntFieldUpdateOperationsInput | number
    version?: StringFieldUpdateOperationsInput | string
    versionName?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    verifyTemplate?: StringFieldUpdateOperationsInput | string
    encryptionKey?: StringFieldUpdateOperationsInput | string
    defaultPrice?: FloatFieldUpdateOperationsInput | number
    downloadLink?: NullableStringFieldUpdateOperationsInput | string | null
    coverUrl?: NullableStringFieldUpdateOperationsInput | string | null
    changelog?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumProductStatusFieldUpdateOperationsInput | $Enums.ProductStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseCreateManyVersionInput = {
    id?: number
    licenseKey: string
    status?: $Enums.LicenseStatus
    verifyInstance?: string | null
    activatedAt?: Date | string | null
    distributorId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuthorizationCreateManyVersionInput = {
    id?: number
    distributorId: number
    customPrice?: number | null
    status?: $Enums.AuthorizationStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type LicenseUpdateWithoutVersionInput = {
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    distributor?: UserUpdateOneRequiredWithoutLicensesNestedInput
  }

  export type LicenseUncheckedUpdateWithoutVersionInput = {
    id?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    distributorId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type LicenseUncheckedUpdateManyWithoutVersionInput = {
    id?: IntFieldUpdateOperationsInput | number
    licenseKey?: StringFieldUpdateOperationsInput | string
    status?: EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
    verifyInstance?: NullableStringFieldUpdateOperationsInput | string | null
    activatedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    distributorId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationUpdateWithoutVersionInput = {
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    distributor?: UserUpdateOneRequiredWithoutAuthorizationsNestedInput
  }

  export type AuthorizationUncheckedUpdateWithoutVersionInput = {
    id?: IntFieldUpdateOperationsInput | number
    distributorId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuthorizationUncheckedUpdateManyWithoutVersionInput = {
    id?: IntFieldUpdateOperationsInput | number
    distributorId?: IntFieldUpdateOperationsInput | number
    customPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: EnumAuthorizationStatusFieldUpdateOperationsInput | $Enums.AuthorizationStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}