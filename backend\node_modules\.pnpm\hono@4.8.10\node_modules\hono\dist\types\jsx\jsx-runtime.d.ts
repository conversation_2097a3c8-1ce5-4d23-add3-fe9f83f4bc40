/**
 * @module
 * This module provides Hono's JSX runtime.
 */
export { jsxDEV as jsx, Fragment } from './jsx-dev-runtime';
export { jsxDEV as jsxs } from './jsx-dev-runtime';
export type { JSX } from './jsx-dev-runtime';
import { html } from '../helper/html';
import type { HtmlEscapedString } from '../utils/html';
export { html as jsxTemplate };
export declare const jsxAttr: (key: string, v: string | Promise<string> | Record<string, string | number | null | undefined | boolean>) => HtmlEscapedString | Promise<HtmlEscapedString>;
export declare const jsxEscape: (value: string) => string;
