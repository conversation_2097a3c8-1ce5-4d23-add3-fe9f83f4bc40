{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "prisma": "^6.13.0", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.27.0"}, "dependencies": {"@prisma/adapter-d1": "^6.13.0", "@prisma/client": "^6.13.0", "hono": "^4.8.10"}}