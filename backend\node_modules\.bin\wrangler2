#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/bin/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/bin/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/wrangler@4.27.0/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/wrangler@4.27.0/node_modules/wrangler/bin/wrangler.js" "$@"
else
  exec node  "$basedir/../.pnpm/wrangler@4.27.0/node_modules/wrangler/bin/wrangler.js" "$@"
fi
