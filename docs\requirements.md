# 软件可用性验证服务系统需求文档

## 1. 系统概述

本系统是一个软件可用性验证的服务，包含了软件分发的功能。整个系统通过许可证（License）管理机制，为软件开发者提供授权验证服务，并支持分发商进行软件销售。

## 2. 角色定义

### 2.1 管理员（开发者）

- **角色**：ADMIN
- **权限**：系统超级管理员
- **职责**：相当于整个系统的超级管理员

### 2.2 分发商

- **角色**：DISTRIBUTOR
- **权限**：管理员权限的子集
- **职责**：对软件进行售卖的人
- **基本信息**：昵称（nickName）、微信（wechat）、头像（avatar）

## 3. 核心功能需求

### 3.1 人员管理（管理员功能）

对人员的增删改查，主要用来管理分发授权，可以授权分发商销售自己开发的某些软件。

**数据模型**：

```
User {
  - 基本信息：用户名、密码哈希、角色、状态
  - 分发商信息：昵称、微信、头像
  - 时间戳：创建时间、更新时间
}
```

### 3.2 软件管理（管理员功能）

#### 3.2.1 产品基础管理

**数据模型**：

```
Product {
  - 基本信息：产品名称、描述、分类
  - 状态：ACTIVE/INACTIVE
}
```

#### 3.2.2 版本管理

**需要的字段包含**：

- 软件名称、封面图、描述
- 版本管理：更新日志、版本号、版本名、定价、下载链接

**具体功能**：

- 版本创建/编辑/删除
- 版本信息管理：
  - 版本号（如：1.0.0）
  - 版本名称（如：正式版）
  - 版本描述
  - 更新日志
  - 下载链接
  - 封面图片
- 验证模板配置（JSON 格式）
- 加密密钥管理
- 默认价格设置

**数据模型**：

```
ProductVersion {
  - 版本信息：版本号、版本名、描述
  - 内容：变更日志、下载链接、封面图
  - 验证配置：验证模板（JSON格式）、加密密钥
  - 定价：默认价格
  - 状态：ACTIVE/INACTIVE
}
```

#### 3.2.3 验证模板配置系统

验证模板配置是动态的，不是固定字段。这些配置是类似于约束，方便转换成前端的 UI 表单。

**支持的约束类型到 UI 组件映射**：

- **boolean** → **Switch 开关**
- **string** → **文本输入框**
- **number** → **数字输入框**
- **date** → **日期选择器**
- **array** → **多选选择器**

**配置格式示例**：

```json
[
  {
    "key": "expiration",
    "type": "date",
    "value": "2024-12-31",
    "default": "2024-12-31"
  },
  {
    "key": "features",
    "type": "array",
    "options": [
      {
        "value": "feature1",
        "label": "特性1"
      },
      {
        "value": "feature2",
        "label": "特性2"
      }
    ],
    "default": ["feature1"]
  },
  {
    "key": "max_devices",
    "type": "number",
    "value": 10,
    "default": 10
  },
  {
    "key": "current_devices",
    "type": "array",
    "value": ["device1", "device2"],
    "default": [],
    "options": [
      {
        "value": "device1",
        "label": "设备1"
      },
      {
        "value": "device2",
        "label": "设备2"
      }
    ]
  },
  {
    "key": "custom_field",
    "type": "string",
    "value": "custom value",
    "default": ""
  }
]
```

### 3.3 分发授权管理

管理员为分发商授权特定软件版本的销售权限。

**数据模型**：

```
Authorization {
  - 授权关系：分发商ID、产品版本ID
  - 定价：自定义价格（可覆盖默认价格）
  - 状态：ACTIVE/INACTIVE
}
```

### 3.4 License 管理

#### 3.4.1 分发商功能

- 对授权的软件生成 license
- 管理 license
- 撤销 license
- 每一个 license 有一个验证配置，这个验证配置是动态的
- 验证配置从服务端获取，整个验证逻辑在客户端本地进行

#### 3.4.2 数据模型

```
License {
  - 许可证信息：License Key、状态、验证实例
  - 关联：产品版本、分发商
  - 时间：创建时间、激活时间
  - 状态：INACTIVE/ACTIVE/REVOKED
}
```

#### 3.4.3 客户端通信安全机制

客户端可以向远端配置提交数据。不管是请求 license 信息还是向上 post license 信息都需要加密。

**重要安全要求**：

- **GET 请求**：在没有 token 的前提下，GET 的响应是加密的
- **POST 请求**：在没有 token 的前提下，POST 的参数是加密的
- 使用产品版本的加密密钥进行加密/解密

### 3.5 店铺展示功能

这些软件信息可以为分发商后续生成自己的店铺。无jiao'yi逻辑，只是一个展示页。销售逻辑由分发商在私域完成，不限制交易方式。

### 3.6 数据统计功能

#### 3.6.1 管理员统计

开发者可以查看所有分销商的业绩，比如说哪个软件卖的好，哪个分销商赚的钱多。

#### 3.6.2 分发商统计

分销商可以对自己售出的授权的产品进行销售业绩的统计。

## 4. 数据库设计

基于提供的 Prisma schema，包含以下核心表：

- **User**：用户表（管理员和分发商）
- **Product**：产品表（基础信息）
- **ProductVersion**：产品版本表（包含验证模板和加密密钥）
- **Authorization**：分发商授权表（店铺商品授权）
- **License**：许可证表

## 5. 关键特性

- **动态验证配置**：验证配置从服务端获取，验证逻辑在客户端本地进行
- **加密通信**：客户端与服务端的 license 相关通信全部加密
- **权限分离**：管理员和分发商角色权限清晰分离
- **灵活定价**：支持版本默认价格和分发商自定义价格
- **无实质交易**：系统仅提供展示和管理，实际交易在私域完成
