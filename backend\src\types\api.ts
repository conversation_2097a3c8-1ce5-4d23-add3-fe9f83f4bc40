/**
 * API 类型定义
 */

// 标准 API 响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
  };
  timestamp: string;
}

// 许可证相关类型
export interface LicenseCreateRequest {
  versionId: number;
  distributorId: number;
  verifyInstance: any; // 验证配置对象
  quantity?: number; // 可选，如果提供则批量创建，否则单个创建
}

export interface LicenseInfo {
  id: number;
  licenseKey: string;
  status: 'INACTIVE' | 'ACTIVE' | 'REVOKED';
  createdAt: string;
  activatedAt?: string;
}

export interface ProductInfo {
  id: number;
  name: string;
  version: string;
  versionName?: string;
  description?: string;
}

// 验证相关类型
export interface VerifyRequest {
  licenseKey: string;
  verifyInstance?: string; // 可选的配置参数
}

export interface VerifyResponse {
  valid: boolean;
  reason?: string;
  message: string;
  productInfo?: ProductInfo;
  verifyInstance?: any; // 客户端配置数据
  activatedAt?: string;
  lastFetched?: string;
  encrypted?: boolean; // 是否加密
  data?: string; // 加密后的数据
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResult {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Cloudflare Workers 环境类型
export interface Env {
  DB: D1Database;
  JWT_SECRET: string;
  API_VERSION: string;
  ALLOWED_ORIGINS: string;
}
